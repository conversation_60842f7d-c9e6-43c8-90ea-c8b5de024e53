const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [2, 50]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: [],
    comment: 'Array of permission strings'
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1,
      max: 10
    },
    comment: 'Role hierarchy level (1=lowest, 10=highest)'
  },
  is_admin: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  is_manager: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  can_approve_leave: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  can_manage_staff: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  can_view_reports: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  can_modify_attendance: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  max_leave_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum leave days this role can approve'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['name']
    },
    {
      fields: ['level']
    },
    {
      fields: ['is_active']
    }
  ]
});

// Instance methods
Role.prototype.hasPermission = function(permission) {
  return this.permissions.includes(permission) || this.is_admin;
};

Role.prototype.canApproveLeave = function(leaveDays) {
  if (!this.can_approve_leave) return false;
  if (this.is_admin) return true;
  if (this.max_leave_days === null) return true;
  
  return leaveDays <= this.max_leave_days;
};

Role.prototype.getPermissionsList = function() {
  const basePermissions = [];
  
  if (this.can_manage_staff) basePermissions.push('manage_staff');
  if (this.can_approve_leave) basePermissions.push('approve_leave');
  if (this.can_view_reports) basePermissions.push('view_reports');
  if (this.can_modify_attendance) basePermissions.push('modify_attendance');
  if (this.is_manager) basePermissions.push('manager_access');
  if (this.is_admin) basePermissions.push('admin_access');
  
  return [...new Set([...basePermissions, ...this.permissions])];
};

module.exports = Role;
