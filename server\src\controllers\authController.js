const { Staff, AuditLog } = require('../models');
const JWTUtils = require('../utils/jwt');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class AuthController {
  // Staff login
  static async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { email, password, employee_id } = req.body;
      
      // Find staff by email or employee_id
      const whereClause = email ? { email } : { employee_id };
      
      const staff = await Staff.findOne({
        where: whereClause,
        include: ['role', 'department']
      });

      if (!staff) {
        await AuditLog.logLogin(null, req.ip, req.get('User-Agent'), false);
        return res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
      }

      // Check if staff is active
      if (staff.status !== 'active') {
        await AuditLog.logLogin(staff.staff_id, req.ip, req.get('User-Agent'), false);
        return res.status(401).json({
          success: false,
          error: 'Account is inactive'
        });
      }

      // Validate password
      const isValidPassword = await staff.validatePassword(password);
      if (!isValidPassword) {
        await AuditLog.logLogin(staff.staff_id, req.ip, req.get('User-Agent'), false);
        return res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
      }

      // Generate tokens
      const payload = JWTUtils.getTokenPayload(staff);
      const { accessToken, refreshToken } = JWTUtils.generateTokens(payload);

      // Store refresh token
      await JWTUtils.storeRefreshToken(staff.staff_id, refreshToken);

      // Update last login
      await staff.update({ last_login: new Date() });

      // Log successful login
      await AuditLog.logLogin(staff.staff_id, req.ip, req.get('User-Agent'), true);
      logger.info(`Staff ${staff.staff_id} logged in successfully`);

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          staff: staff.toJSON(),
          tokens: {
            accessToken,
            refreshToken,
            expiresIn: process.env.JWT_EXPIRES_IN || '24h'
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Refresh access token
  static async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          error: 'Refresh token is required'
        });
      }

      const { accessToken } = await JWTUtils.refreshAccessToken(refreshToken);

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken,
          expiresIn: process.env.JWT_EXPIRES_IN || '24h'
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(401).json({
        success: false,
        error: error.message
      });
    }
  }

  // Staff logout
  static async logout(req, res) {
    try {
      const staffId = req.user.staff_id;
      const token = req.token;

      // Blacklist current token
      await JWTUtils.blacklistToken(token);

      // Remove refresh token
      await JWTUtils.removeRefreshToken(staffId);

      // Log logout
      await AuditLog.logLogout(staffId, req.ip);
      logger.info(`Staff ${staffId} logged out successfully`);

      res.json({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get current user profile
  static async getProfile(req, res) {
    try {
      const staff = await Staff.findByPk(req.user.staff_id, {
        include: ['role', 'department', 'biometric']
      });

      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      res.json({
        success: true,
        data: {
          staff: staff.toJSON(),
          permissions: staff.role?.getPermissionsList() || []
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Update profile
  static async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const staffId = req.user.staff_id;
      const allowedFields = [
        'phone', 'address', 'emergency_contact_name', 
        'emergency_contact_phone', 'profile_picture'
      ];

      // Filter allowed fields
      const updateData = {};
      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });

      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No valid fields to update'
        });
      }

      const staff = await Staff.findByPk(staffId);
      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      const oldValues = staff.toJSON();
      await staff.update(updateData);

      // Log the update
      await AuditLog.logStaffAction(
        staffId, 
        'update_profile', 
        staff.toJSON(), 
        oldValues, 
        req.ip
      );

      logger.info(`Staff ${staffId} updated profile`);

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: staff.toJSON()
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Change password
  static async changePassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const staffId = req.user.staff_id;

      const staff = await Staff.findByPk(staffId);
      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      // Validate current password
      const isValidPassword = await staff.validatePassword(currentPassword);
      if (!isValidPassword) {
        logger.logSecurity('password_change_failed', {
          staff_id: staffId,
          reason: 'Invalid current password'
        }, req);

        return res.status(400).json({
          success: false,
          error: 'Current password is incorrect'
        });
      }

      // Update password
      await staff.update({ password: newPassword });

      // Blacklist all existing tokens for this user
      await JWTUtils.removeRefreshToken(staffId);

      // Log password change
      await AuditLog.logStaffAction(
        staffId, 
        'change_password', 
        { staff_id: staffId }, 
        null, 
        req.ip
      );

      logger.logSecurity('password_changed', { staff_id: staffId }, req);

      res.json({
        success: true,
        message: 'Password changed successfully. Please login again.'
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Verify token (for client-side token validation)
  static async verifyToken(req, res) {
    try {
      // If we reach here, token is valid (middleware already verified it)
      res.json({
        success: true,
        message: 'Token is valid',
        data: {
          staff_id: req.user.staff_id,
          email: req.user.email,
          role: req.user.role?.name,
          permissions: req.user.role?.getPermissionsList() || []
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = AuthController;
