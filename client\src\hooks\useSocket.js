import { useEffect, useRef } from 'react'
import { io } from 'socket.io-client'
import toast from 'react-hot-toast'
import { useAuthStore } from '../stores/authStore'

export const useSocketConnection = (isAuthenticated) => {
  const socketRef = useRef(null)
  const { user, isAdmin, isManager } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated || !user) {
      // Disconnect socket if not authenticated
      if (socketRef.current) {
        socketRef.current.disconnect()
        socketRef.current = null
      }
      return
    }

    // Create socket connection
    const socket = io(import.meta.env.VITE_SOCKET_URL || window.location.origin, {
      auth: {
        token: useAuthStore.getState().tokens?.accessToken
      },
      transports: ['websocket', 'polling']
    })

    socketRef.current = socket

    // Connection events
    socket.on('connect', () => {
      console.log('Socket connected:', socket.id)
      
      // Join admin room if user is admin/manager
      if (isAdmin() || isManager()) {
        socket.emit('join-admin')
      }
    })

    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
    })

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
    })

    // Real-time event handlers
    socket.on('staff_created', (data) => {
      if (isAdmin() || isManager()) {
        toast.success(`New staff member ${data.staff.first_name} ${data.staff.last_name} created`)
      }
    })

    socket.on('staff_updated', (data) => {
      if (isAdmin() || isManager()) {
        toast.success(`Staff member ${data.staff.first_name} ${data.staff.last_name} updated`)
      }
    })

    socket.on('staff_deleted', (data) => {
      if (isAdmin() || isManager()) {
        toast.success(`Staff member deleted`)
      }
    })

    socket.on('attendance_checkin', (data) => {
      if (isAdmin() || isManager()) {
        const staff = data.staff
        const isLate = data.attendance.status === 'late'
        toast.success(
          `${staff.first_name} ${staff.last_name} checked in${isLate ? ' (Late)' : ''}`,
          {
            icon: isLate ? '⚠️' : '✅'
          }
        )
      }
    })

    socket.on('attendance_checkout', (data) => {
      if (isAdmin() || isManager()) {
        const staff = data.staff
        toast.success(`${staff.first_name} ${staff.last_name} checked out`)
      }
    })

    socket.on('leave_applied', (data) => {
      if (isAdmin() || isManager()) {
        const staff = data.staff
        toast.success(`New leave application from ${staff.first_name} ${staff.last_name}`)
      }
    })

    socket.on('leave_processed', (data) => {
      const staff = data.leave.staff
      const status = data.status
      
      // Notify the staff member who applied for leave
      if (user.staff_id === data.leave.staff_id) {
        toast.success(`Your leave application has been ${status}`)
      }
      
      // Notify admins/managers
      if (isAdmin() || isManager()) {
        toast.success(`Leave ${status} for ${staff?.first_name} ${staff?.last_name}`)
      }
    })

    socket.on('biometric_enrolled', (data) => {
      if (isAdmin() || isManager()) {
        toast.success(`Biometric enrollment completed for staff ${data.staff_id}`)
      }
    })

    // System notifications
    socket.on('system_notification', (data) => {
      const { type, message, title } = data
      
      switch (type) {
        case 'info':
          toast.success(message)
          break
        case 'warning':
          toast.error(message)
          break
        case 'error':
          toast.error(message)
          break
        default:
          toast(message)
      }
    })

    // Device status updates
    socket.on('device_status', (data) => {
      if (isAdmin() || isManager()) {
        const { deviceId, status } = data
        if (status === 'disconnected') {
          toast.error(`Biometric device ${deviceId} disconnected`)
        } else if (status === 'connected') {
          toast.success(`Biometric device ${deviceId} connected`)
        }
      }
    })

    // Cleanup on unmount
    return () => {
      if (socket) {
        socket.disconnect()
      }
    }
  }, [isAuthenticated, user])

  return socketRef.current
}

// Hook for emitting socket events
export const useSocket = () => {
  const { isAuthenticated } = useAuthStore()
  const socketRef = useRef(null)

  useEffect(() => {
    if (isAuthenticated) {
      socketRef.current = io(import.meta.env.VITE_SOCKET_URL || window.location.origin)
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect()
      }
    }
  }, [isAuthenticated])

  const emit = (event, data) => {
    if (socketRef.current) {
      socketRef.current.emit(event, data)
    }
  }

  const on = (event, callback) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback)
    }
  }

  const off = (event, callback) => {
    if (socketRef.current) {
      socketRef.current.off(event, callback)
    }
  }

  return {
    socket: socketRef.current,
    emit,
    on,
    off,
    isConnected: socketRef.current?.connected || false
  }
}
