const sequelize = require('../config/database');
const Staff = require('./Staff');
const Attendance = require('./Attendance');
const Leave = require('./Leave');
const BiometricData = require('./BiometricData');
const Department = require('./Department');
const Role = require('./Role');
const AuditLog = require('./AuditLog');

// Define associations
const defineAssociations = () => {
  // Department associations
  Department.hasMany(Staff, { foreignKey: 'department_id', as: 'staff' });
  Staff.belongsTo(Department, { foreignKey: 'department_id', as: 'department' });

  // Role associations
  Role.hasMany(Staff, { foreignKey: 'role_id', as: 'staff' });
  Staff.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });

  // Staff associations
  Staff.hasMany(Attendance, { foreignKey: 'staff_id', as: 'attendances' });
  Attendance.belongsTo(Staff, { foreignKey: 'staff_id', as: 'staff' });

  Staff.hasMany(Leave, { foreignKey: 'staff_id', as: 'leaves' });
  Leave.belongsTo(Staff, { foreignKey: 'staff_id', as: 'staff' });

  Staff.hasOne(BiometricData, { foreignKey: 'staff_id', as: 'biometric' });
  BiometricData.belongsTo(Staff, { foreignKey: 'staff_id', as: 'staff' });

  // Leave approval associations
  Leave.belongsTo(Staff, { foreignKey: 'approved_by', as: 'approver' });

  // Audit log associations
  AuditLog.belongsTo(Staff, { foreignKey: 'staff_id', as: 'staff' });
};

// Initialize associations
defineAssociations();

module.exports = {
  sequelize,
  Staff,
  Attendance,
  Leave,
  BiometricData,
  Department,
  Role,
  AuditLog
};
