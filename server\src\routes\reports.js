const express = require('express');
const { query } = require('express-validator');
const ReportsController = require('../controllers/reportsController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation rules
const dateRangeValidation = [
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Valid start date is required'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required'),
  query('staff_id')
    .optional()
    .notEmpty()
    .withMessage('Staff ID cannot be empty'),
  query('department_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid department ID is required')
];

// Routes
router.get('/attendance', 
  dateRangeValidation, 
  authorize('view_reports'), 
  ReportsController.getAttendanceReport
);

router.get('/leave', 
  dateRangeValidation, 
  authorize('view_reports'), 
  ReportsController.getLeaveReport
);

router.get('/staff', 
  authorize('view_reports'), 
  ReportsController.getStaffReport
);

router.get('/dashboard', 
  authorize('view_reports'), 
  ReportsController.getDashboardStats
);

router.get('/export/attendance', 
  dateRangeValidation, 
  authorize('export_reports'), 
  ReportsController.exportAttendanceReport
);

router.get('/export/leave', 
  dateRangeValidation, 
  authorize('export_reports'), 
  ReportsController.exportLeaveReport
);

module.exports = router;
