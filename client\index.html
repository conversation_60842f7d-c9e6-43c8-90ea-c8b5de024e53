<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Attendance Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- Login Page -->
        <div id="loginPage" class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div class="sm:mx-auto sm:w-full sm:max-w-md">
                <div class="flex justify-center">
                    <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Staff Attendance System
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Biometric fingerprint attendance management
                </p>
            </div>

            <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    <!-- Login Method Toggle -->
                    <div class="mb-6">
                        <div class="flex rounded-md shadow-sm" role="group">
                            <button type="button" id="emailBtn"
                                    class="px-4 py-2 text-sm font-medium rounded-l-lg border bg-blue-600 text-white border-blue-600">
                                Email
                            </button>
                            <button type="button" id="employeeIdBtn"
                                    class="px-4 py-2 text-sm font-medium rounded-r-lg border-t border-r border-b bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                                Employee ID
                            </button>
                        </div>
                    </div>

                    <form id="loginForm" class="space-y-6">
                        <div>
                            <label for="identifier" class="block text-sm font-medium text-gray-700" id="identifierLabel">
                                Email address
                            </label>
                            <input id="identifier" name="identifier" type="email" required
                                   class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                   placeholder="Enter your email address"
                                   value="<EMAIL>">
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                Password
                            </label>
                            <input id="password" name="password" type="password" required
                                   class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                   placeholder="Enter your password"
                                   value="admin123">
                        </div>

                        <div id="errorMessage" class="hidden rounded-md bg-red-50 p-4">
                            <div class="text-sm text-red-700"></div>
                        </div>

                        <div>
                            <button type="submit" id="loginBtn"
                                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span id="loginText">Sign in</span>
                                <div id="loginSpinner" class="spinner hidden ml-2"></div>
                            </button>
                        </div>
                    </form>

                    <div class="mt-6 p-4 bg-gray-50 rounded-md">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Demo Credentials:</h3>
                        <div class="text-xs text-gray-600 space-y-1">
                            <div><strong>Admin:</strong> <EMAIL> / admin123</div>
                            <div><strong>HR Manager:</strong> <EMAIL> / password123</div>
                            <div><strong>IT Manager:</strong> <EMAIL> / password123</div>
                            <div><strong>Finance Lead:</strong> <EMAIL> / password123</div>
                            <div><strong>Marketing:</strong> <EMAIL> / password123</div>
                            <div><strong>Or use Employee ID:</strong> EMP002, EMP003, EMP004, EMP005 / password123</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboardPage" class="hidden min-h-screen bg-gray-50">
            <nav class="bg-white shadow">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-lg font-semibold text-gray-900">Staff Attendance</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span id="userName" class="text-sm text-gray-700"></span>
                            <span id="userRole" class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"></span>
                            <button id="logoutBtn" class="text-sm text-gray-500 hover:text-gray-700">Logout</button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Admin Navigation Tabs -->
            <div id="adminTabs" class="hidden bg-white border-b">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav class="flex space-x-8">
                        <button id="dashboardTab" class="tab-button active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                            Dashboard
                        </button>
                        <button id="staffManagementTab" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            Staff Management
                        </button>
                        <button id="biometricTab" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            Biometric Enrollment
                        </button>
                        <button id="attendanceTab" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            Attendance
                        </button>
                        <button id="reportsTab" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            Reports
                        </button>
                    </nav>
                </div>
            </div>

            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Dashboard Content -->
                    <div id="dashboardContent" class="tab-content">
                        <h1 class="text-2xl font-bold text-gray-900 mb-6">Dashboard</h1>

                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Staff</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="totalStaff">10</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Present Today</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="presentToday">8</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Late Today</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="lateToday">2</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Leaves</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="pendingLeaves">3</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Welcome Message -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Welcome to Staff Attendance Management System</h2>
                        <p class="text-gray-600 mb-4">
                            🎉 <strong>System is running successfully!</strong>
                        </p>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p>✅ Backend API: Connected and working</p>
                            <p>✅ Database: SQLite database created with sample data</p>
                            <p>✅ Authentication: JWT-based login system</p>
                            <p>✅ Real-time Updates: WebSocket connections ready</p>
                            <p>✅ Biometric Simulation: Fingerprint system ready</p>
                        </div>

                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <h3 class="text-sm font-medium text-blue-900 mb-2">Next Steps:</h3>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Complete frontend React components</li>
                                <li>• Integrate real biometric devices</li>
                                <li>• Add advanced reporting features</li>
                                <li>• Deploy to production environment</li>
                            </ul>
                        </div>
                    </div>
                    </div>

                    <!-- Staff Management Content -->
                    <div id="staffManagementContent" class="tab-content hidden">
                        <div class="flex justify-between items-center mb-6">
                            <h1 class="text-2xl font-bold text-gray-900">Staff Management</h1>
                            <button id="addStaffBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Add New Staff
                            </button>
                        </div>

                        <!-- Staff List -->
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">Staff Members</h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Biometric</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="staffTableBody" class="bg-white divide-y divide-gray-200">
                                        <!-- Staff rows will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Biometric Enrollment Content -->
                    <div id="biometricContent" class="tab-content hidden">
                        <h1 class="text-2xl font-bold text-gray-900 mb-6">Biometric Enrollment</h1>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Enrollment Form -->
                            <div class="bg-white shadow rounded-lg p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Enroll Fingerprint</h3>
                                <form id="biometricForm" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Staff Member</label>
                                        <select id="biometricStaffSelect" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">Select Staff Member</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Finger Position</label>
                                        <select id="fingerPosition" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="right_index">Right Index Finger</option>
                                            <option value="right_thumb">Right Thumb</option>
                                            <option value="left_index">Left Index Finger</option>
                                            <option value="left_thumb">Left Thumb</option>
                                        </select>
                                    </div>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                        <div id="fingerprintScanner" class="space-y-4">
                                            <div class="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                                                <svg class="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                                </svg>
                                            </div>
                                            <p class="text-sm text-gray-600">Place finger on scanner</p>
                                            <button type="button" id="scanFingerprintBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                                Start Scan
                                            </button>
                                        </div>
                                        <div id="scanningProgress" class="hidden space-y-4">
                                            <div class="w-24 h-24 mx-auto bg-green-100 rounded-full flex items-center justify-center animate-pulse">
                                                <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                            <p class="text-sm text-green-600">Scanning fingerprint...</p>
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div id="scanProgress" class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="submit" id="enrollBtn" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50" disabled>
                                        Enroll Fingerprint
                                    </button>
                                </form>
                            </div>

                            <!-- Enrollment Status -->
                            <div class="bg-white shadow rounded-lg p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Enrollment Status</h3>
                                <div id="enrollmentStatus" class="space-y-4">
                                    <div class="text-center py-8 text-gray-500">
                                        Select a staff member to view enrollment status
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Content -->
                    <div id="attendanceContent" class="tab-content hidden">
                        <h1 class="text-2xl font-bold text-gray-900 mb-6">Attendance Management</h1>
                        <div class="bg-white shadow rounded-lg p-6">
                            <p class="text-gray-600">Attendance management features will be implemented here.</p>
                        </div>
                    </div>

                    <!-- Reports Content -->
                    <div id="reportsContent" class="tab-content hidden">
                        <h1 class="text-2xl font-bold text-gray-900 mb-6">Reports & Analytics</h1>
                        <div class="bg-white shadow rounded-lg p-6">
                            <p class="text-gray-600">Reports and analytics features will be implemented here.</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Add Staff Modal -->
        <div id="addStaffModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Add New Staff Member</h3>
                    <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="addStaffForm" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">First Name *</label>
                            <input type="text" id="firstName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Last Name *</label>
                            <input type="text" id="lastName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email *</label>
                            <input type="email" id="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Employee ID *</label>
                            <input type="text" id="employeeId" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <input type="tel" id="phone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Position *</label>
                            <input type="text" id="position" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Department *</label>
                            <select id="department" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Department</option>
                                <option value="HR">Human Resources</option>
                                <option value="IT">Information Technology</option>
                                <option value="Finance">Finance</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Operations">Operations</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Role *</label>
                            <select id="role" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Role</option>
                                <option value="Employee">Employee</option>
                                <option value="Senior Employee">Senior Employee</option>
                                <option value="Team Lead">Team Lead</option>
                                <option value="Manager">Manager</option>
                                <option value="Admin">Admin</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Hire Date *</label>
                            <input type="date" id="hireDate" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Salary</label>
                            <input type="number" id="salary" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Add Staff Member
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Simple JavaScript for demo functionality
        const API_BASE = 'http://localhost:5000/api';
        let currentUser = null;
        let loginMethod = 'email'; // 'email' or 'employee_id'

        // DOM elements
        const loginPage = document.getElementById('loginPage');
        const dashboardPage = document.getElementById('dashboardPage');
        const loginForm = document.getElementById('loginForm');
        const errorMessage = document.getElementById('errorMessage');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');
        const userName = document.getElementById('userName');
        const logoutBtn = document.getElementById('logoutBtn');
        const emailBtn = document.getElementById('emailBtn');
        const employeeIdBtn = document.getElementById('employeeIdBtn');
        const identifierInput = document.getElementById('identifier');
        const identifierLabel = document.getElementById('identifierLabel');

        // Admin elements
        const adminTabs = document.getElementById('adminTabs');
        const userRole = document.getElementById('userRole');
        const addStaffBtn = document.getElementById('addStaffBtn');
        const addStaffModal = document.getElementById('addStaffModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const addStaffForm = document.getElementById('addStaffForm');

        // Mock staff data
        let staffList = [
            { id: 1, firstName: 'System', lastName: 'Administrator', email: '<EMAIL>', employeeId: 'EMP001', role: 'Super Admin', department: 'IT', biometric: true },
            { id: 2, firstName: 'Sarah', lastName: 'Johnson', email: '<EMAIL>', employeeId: 'EMP002', role: 'HR Manager', department: 'HR', biometric: true },
            { id: 3, firstName: 'Michael', lastName: 'Chen', email: '<EMAIL>', employeeId: 'EMP003', role: 'IT Manager', department: 'IT', biometric: false },
            { id: 4, firstName: 'Emily', lastName: 'Davis', email: '<EMAIL>', employeeId: 'EMP004', role: 'Finance Team Lead', department: 'Finance', biometric: false },
            { id: 5, firstName: 'David', lastName: 'Wilson', email: '<EMAIL>', employeeId: 'EMP005', role: 'Senior Marketing Specialist', department: 'Marketing', biometric: false }
        ];

        // Check if user is already logged in
        const token = localStorage.getItem('accessToken');
        if (token) {
            showDashboard();
        }

        // Login method toggle handlers
        emailBtn.addEventListener('click', () => {
            loginMethod = 'email';
            emailBtn.className = 'px-4 py-2 text-sm font-medium rounded-l-lg border bg-blue-600 text-white border-blue-600';
            employeeIdBtn.className = 'px-4 py-2 text-sm font-medium rounded-r-lg border-t border-r border-b bg-white text-gray-700 border-gray-300 hover:bg-gray-50';
            identifierLabel.textContent = 'Email address';
            identifierInput.type = 'email';
            identifierInput.placeholder = 'Enter your email address';
            identifierInput.value = '<EMAIL>';
        });

        employeeIdBtn.addEventListener('click', () => {
            loginMethod = 'employee_id';
            employeeIdBtn.className = 'px-4 py-2 text-sm font-medium rounded-r-lg border bg-blue-600 text-white border-blue-600';
            emailBtn.className = 'px-4 py-2 text-sm font-medium rounded-l-lg border-t border-l border-b bg-white text-gray-700 border-gray-300 hover:bg-gray-50';
            identifierLabel.textContent = 'Employee ID';
            identifierInput.type = 'text';
            identifierInput.placeholder = 'Enter your employee ID';
            identifierInput.value = 'EMP001';
        });

        // Login form handler
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const identifier = document.getElementById('identifier').value;
            const password = document.getElementById('password').value;

            setLoading(true);
            hideError();

            // Prepare login data based on method
            const loginData = { password };
            if (loginMethod === 'email') {
                loginData.email = identifier;
            } else {
                loginData.employee_id = identifier;
            }

            console.log('Sending login data:', loginData);

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData),
                });

                const data = await response.json();
                console.log('Login response:', data);

                if (data.success) {
                    currentUser = data.data.staff;
                    localStorage.setItem('accessToken', data.data.tokens.accessToken);
                    showDashboard();
                    showSuccess(`Welcome ${currentUser.first_name} ${currentUser.last_name}!`);
                } else {
                    showError(data.error || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Connection error. Please check if the server is running.');
            } finally {
                setLoading(false);
            }
        });

        // Logout handler
        logoutBtn.addEventListener('click', () => {
            localStorage.removeItem('accessToken');
            currentUser = null;
            showLogin();
        });

        // Tab switching functionality
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.id.replace('Tab', 'Content');

                    // Update button states
                    tabButtons.forEach(btn => {
                        btn.className = 'tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300';
                    });
                    button.className = 'tab-button active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600';

                    // Update content visibility
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    document.getElementById(tabId).classList.remove('hidden');

                    // Load content based on tab
                    if (tabId === 'staffManagementContent') {
                        loadStaffList();
                    } else if (tabId === 'biometricContent') {
                        loadBiometricStaffList();
                    }
                });
            });
        }

        // Modal handlers
        if (addStaffBtn) {
            addStaffBtn.addEventListener('click', () => {
                addStaffModal.classList.remove('hidden');
                // Set default hire date to today
                document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
            });
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                addStaffModal.classList.add('hidden');
                addStaffForm.reset();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                addStaffModal.classList.add('hidden');
                addStaffForm.reset();
            });
        }

        // Add staff form handler
        if (addStaffForm) {
            addStaffForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addNewStaff();
            });
        }

        function setLoading(loading) {
            if (loading) {
                loginText.textContent = 'Signing in...';
                loginSpinner.classList.remove('hidden');
                loginBtn.disabled = true;
            } else {
                loginText.textContent = 'Sign in';
                loginSpinner.classList.add('hidden');
                loginBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.querySelector('div').textContent = message;
            errorMessage.classList.remove('hidden');
        }

        function hideError() {
            errorMessage.classList.add('hidden');
        }

        function showSuccess(message) {
            // Simple success notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function showLogin() {
            loginPage.classList.remove('hidden');
            dashboardPage.classList.add('hidden');
        }

        function showDashboard() {
            loginPage.classList.add('hidden');
            dashboardPage.classList.remove('hidden');

            if (currentUser) {
                userName.textContent = `${currentUser.first_name} ${currentUser.last_name}`;
                userRole.textContent = currentUser.role.name;

                // Show admin tabs if user is admin
                if (currentUser.role.is_admin) {
                    adminTabs.classList.remove('hidden');
                    initializeTabs();
                } else {
                    adminTabs.classList.add('hidden');
                }
            }

            // Load dashboard data
            loadDashboardData();
        }

        // Staff management functions
        function loadStaffList() {
            const tbody = document.getElementById('staffTableBody');
            tbody.innerHTML = '';

            staffList.forEach(staff => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-700">${staff.firstName[0]}${staff.lastName[0]}</span>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${staff.firstName} ${staff.lastName}</div>
                                <div class="text-sm text-gray-500">${staff.email}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${staff.employeeId}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${staff.role}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${staff.department}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${staff.biometric ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${staff.biometric ? 'Enrolled' : 'Not Enrolled'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="editStaff(${staff.id})" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                        <button onclick="deleteStaff(${staff.id})" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function addNewStaff() {
            const formData = new FormData(addStaffForm);
            const newStaff = {
                id: staffList.length + 1,
                firstName: formData.get('firstName') || document.getElementById('firstName').value,
                lastName: formData.get('lastName') || document.getElementById('lastName').value,
                email: formData.get('email') || document.getElementById('email').value,
                employeeId: formData.get('employeeId') || document.getElementById('employeeId').value,
                phone: formData.get('phone') || document.getElementById('phone').value,
                position: formData.get('position') || document.getElementById('position').value,
                department: formData.get('department') || document.getElementById('department').value,
                role: formData.get('role') || document.getElementById('role').value,
                hireDate: formData.get('hireDate') || document.getElementById('hireDate').value,
                salary: formData.get('salary') || document.getElementById('salary').value,
                biometric: false
            };

            // Generate unique employee ID if not provided
            if (!newStaff.employeeId) {
                newStaff.employeeId = `EMP${String(staffList.length + 1).padStart(3, '0')}`;
            }

            staffList.push(newStaff);
            addStaffModal.classList.add('hidden');
            addStaffForm.reset();
            loadStaffList();
            showSuccess(`Staff member ${newStaff.firstName} ${newStaff.lastName} added successfully!`);
        }

        function editStaff(id) {
            const staff = staffList.find(s => s.id === id);
            if (staff) {
                showSuccess(`Edit functionality for ${staff.firstName} ${staff.lastName} will be implemented.`);
            }
        }

        function deleteStaff(id) {
            if (confirm('Are you sure you want to delete this staff member?')) {
                const index = staffList.findIndex(s => s.id === id);
                if (index > -1) {
                    const staff = staffList[index];
                    staffList.splice(index, 1);
                    loadStaffList();
                    showSuccess(`Staff member ${staff.firstName} ${staff.lastName} deleted successfully!`);
                }
            }
        }

        async function loadDashboardData() {
            try {
                const token = localStorage.getItem('accessToken');
                const response = await fetch(`${API_BASE}/reports/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    updateDashboardStats(data.data);
                }
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            }
        }

        function updateDashboardStats(data) {
            if (data.staff) {
                document.getElementById('totalStaff').textContent = data.staff.total_staff || staffList.length;
            }
            if (data.today) {
                document.getElementById('presentToday').textContent = data.today.total_present || '8';
                document.getElementById('lateToday').textContent = data.today.total_late || '2';
            }
            if (data.leaves) {
                document.getElementById('pendingLeaves').textContent = data.leaves.pending_approvals || '3';
            }
        }

        // Biometric enrollment functions
        function loadBiometricStaffList() {
            const select = document.getElementById('biometricStaffSelect');
            select.innerHTML = '<option value="">Select Staff Member</option>';

            staffList.forEach(staff => {
                const option = document.createElement('option');
                option.value = staff.id;
                option.textContent = `${staff.firstName} ${staff.lastName} (${staff.employeeId})`;
                option.disabled = staff.biometric;
                if (staff.biometric) {
                    option.textContent += ' - Already Enrolled';
                }
                select.appendChild(option);
            });

            // Add event listener for staff selection
            select.addEventListener('change', updateEnrollmentStatus);
        }

        function updateEnrollmentStatus() {
            const staffId = document.getElementById('biometricStaffSelect').value;
            const statusDiv = document.getElementById('enrollmentStatus');

            if (!staffId) {
                statusDiv.innerHTML = '<div class="text-center py-8 text-gray-500">Select a staff member to view enrollment status</div>';
                return;
            }

            const staff = staffList.find(s => s.id == staffId);
            if (staff) {
                statusDiv.innerHTML = `
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">${staff.firstName} ${staff.lastName}</h4>
                                <p class="text-sm text-gray-600">${staff.employeeId} - ${staff.department}</p>
                            </div>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full ${staff.biometric ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${staff.biometric ? 'Enrolled' : 'Not Enrolled'}
                            </span>
                        </div>
                        ${staff.biometric ?
                            '<div class="p-4 bg-green-50 rounded-lg"><p class="text-sm text-green-700">✓ Fingerprint already enrolled for this staff member</p></div>' :
                            '<div class="p-4 bg-yellow-50 rounded-lg"><p class="text-sm text-yellow-700">⚠ No fingerprint enrolled. Please scan fingerprint to enroll.</p></div>'
                        }
                    </div>
                `;
            }
        }

        // Fingerprint scanning simulation
        let scanningInterval;
        let scanProgress = 0;

        function startFingerprintScan() {
            const staffId = document.getElementById('biometricStaffSelect').value;
            if (!staffId) {
                showError('Please select a staff member first');
                return;
            }

            const staff = staffList.find(s => s.id == staffId);
            if (staff && staff.biometric) {
                showError('This staff member is already enrolled');
                return;
            }

            // Hide scanner, show progress
            document.getElementById('fingerprintScanner').classList.add('hidden');
            document.getElementById('scanningProgress').classList.remove('hidden');

            scanProgress = 0;
            const progressBar = document.getElementById('scanProgress');

            scanningInterval = setInterval(() => {
                scanProgress += Math.random() * 15 + 5; // Random progress between 5-20%
                progressBar.style.width = Math.min(scanProgress, 100) + '%';

                if (scanProgress >= 100) {
                    clearInterval(scanningInterval);
                    completeFingerprintScan();
                }
            }, 200);
        }

        function completeFingerprintScan() {
            // Show success state
            document.getElementById('scanningProgress').innerHTML = `
                <div class="space-y-4">
                    <div class="w-24 h-24 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-sm text-green-600">Fingerprint captured successfully!</p>
                    <div class="text-xs text-gray-500">Quality: 95% - Excellent</div>
                </div>
            `;

            // Enable enroll button
            document.getElementById('enrollBtn').disabled = false;
            showSuccess('Fingerprint scanned successfully! Click "Enroll Fingerprint" to complete enrollment.');
        }

        function enrollFingerprint() {
            const staffId = document.getElementById('biometricStaffSelect').value;
            const fingerPosition = document.getElementById('fingerPosition').value;

            if (!staffId) {
                showError('Please select a staff member');
                return;
            }

            const staff = staffList.find(s => s.id == staffId);
            if (staff) {
                // Update staff biometric status
                staff.biometric = true;

                // Reset form
                resetBiometricForm();

                // Update displays
                loadBiometricStaffList();
                updateEnrollmentStatus();

                showSuccess(`Fingerprint enrolled successfully for ${staff.firstName} ${staff.lastName}!`);
            }
        }

        function resetBiometricForm() {
            document.getElementById('fingerprintScanner').classList.remove('hidden');
            document.getElementById('scanningProgress').classList.add('hidden');
            document.getElementById('enrollBtn').disabled = true;
            document.getElementById('scanProgress').style.width = '0%';
            scanProgress = 0;
        }

        // Event listeners for biometric functionality
        document.addEventListener('DOMContentLoaded', () => {
            const scanBtn = document.getElementById('scanFingerprintBtn');
            const enrollBtn = document.getElementById('enrollBtn');
            const biometricForm = document.getElementById('biometricForm');

            if (scanBtn) {
                scanBtn.addEventListener('click', startFingerprintScan);
            }

            if (biometricForm) {
                biometricForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    enrollFingerprint();
                });
            }
        });

        // Test server connection on page load
        async function testConnection() {
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    console.log('✅ Server connection successful');
                } else {
                    console.log('⚠️ Server responded but with error');
                }
            } catch (error) {
                console.log('❌ Server connection failed:', error.message);
            }
        }

        testConnection();
    </script>
</body>
</html>
