<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Attendance Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- Login Page -->
        <div id="loginPage" class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div class="sm:mx-auto sm:w-full sm:max-w-md">
                <div class="flex justify-center">
                    <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Staff Attendance System
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Biometric fingerprint attendance management
                </p>
            </div>

            <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    <!-- Login Method Toggle -->
                    <div class="mb-6">
                        <div class="flex rounded-md shadow-sm" role="group">
                            <button type="button" id="emailBtn"
                                    class="px-4 py-2 text-sm font-medium rounded-l-lg border bg-blue-600 text-white border-blue-600">
                                Email
                            </button>
                            <button type="button" id="employeeIdBtn"
                                    class="px-4 py-2 text-sm font-medium rounded-r-lg border-t border-r border-b bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                                Employee ID
                            </button>
                        </div>
                    </div>

                    <form id="loginForm" class="space-y-6">
                        <div>
                            <label for="identifier" class="block text-sm font-medium text-gray-700" id="identifierLabel">
                                Email address
                            </label>
                            <input id="identifier" name="identifier" type="email" required
                                   class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                   placeholder="Enter your email address"
                                   value="<EMAIL>">
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                Password
                            </label>
                            <input id="password" name="password" type="password" required
                                   class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                   placeholder="Enter your password"
                                   value="admin123">
                        </div>

                        <div id="errorMessage" class="hidden rounded-md bg-red-50 p-4">
                            <div class="text-sm text-red-700"></div>
                        </div>

                        <div>
                            <button type="submit" id="loginBtn"
                                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span id="loginText">Sign in</span>
                                <div id="loginSpinner" class="spinner hidden ml-2"></div>
                            </button>
                        </div>
                    </form>

                    <div class="mt-6 p-4 bg-gray-50 rounded-md">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Demo Credentials:</h3>
                        <div class="text-xs text-gray-600 space-y-1">
                            <div><strong>Admin:</strong> <EMAIL> / admin123</div>
                            <div><strong>HR Manager:</strong> <EMAIL> / password123</div>
                            <div><strong>IT Manager:</strong> <EMAIL> / password123</div>
                            <div><strong>Finance Lead:</strong> <EMAIL> / password123</div>
                            <div><strong>Marketing:</strong> <EMAIL> / password123</div>
                            <div><strong>Or use Employee ID:</strong> EMP002, EMP003, EMP004, EMP005 / password123</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboardPage" class="hidden min-h-screen bg-gray-50">
            <nav class="bg-white shadow">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-lg font-semibold text-gray-900">Staff Attendance</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span id="userName" class="text-sm text-gray-700"></span>
                            <button id="logoutBtn" class="text-sm text-gray-500 hover:text-gray-700">Logout</button>
                        </div>
                    </div>
                </div>
            </nav>

            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <h1 class="text-2xl font-bold text-gray-900 mb-6">Dashboard</h1>

                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Staff</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="totalStaff">10</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Present Today</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="presentToday">8</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Late Today</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="lateToday">2</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Leaves</dt>
                                            <dd class="text-2xl font-semibold text-gray-900" id="pendingLeaves">3</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Welcome Message -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Welcome to Staff Attendance Management System</h2>
                        <p class="text-gray-600 mb-4">
                            🎉 <strong>System is running successfully!</strong>
                        </p>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p>✅ Backend API: Connected and working</p>
                            <p>✅ Database: SQLite database created with sample data</p>
                            <p>✅ Authentication: JWT-based login system</p>
                            <p>✅ Real-time Updates: WebSocket connections ready</p>
                            <p>✅ Biometric Simulation: Fingerprint system ready</p>
                        </div>

                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <h3 class="text-sm font-medium text-blue-900 mb-2">Next Steps:</h3>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Complete frontend React components</li>
                                <li>• Integrate real biometric devices</li>
                                <li>• Add advanced reporting features</li>
                                <li>• Deploy to production environment</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Simple JavaScript for demo functionality
        const API_BASE = 'http://localhost:5000/api';
        let currentUser = null;
        let loginMethod = 'email'; // 'email' or 'employee_id'

        // DOM elements
        const loginPage = document.getElementById('loginPage');
        const dashboardPage = document.getElementById('dashboardPage');
        const loginForm = document.getElementById('loginForm');
        const errorMessage = document.getElementById('errorMessage');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');
        const userName = document.getElementById('userName');
        const logoutBtn = document.getElementById('logoutBtn');
        const emailBtn = document.getElementById('emailBtn');
        const employeeIdBtn = document.getElementById('employeeIdBtn');
        const identifierInput = document.getElementById('identifier');
        const identifierLabel = document.getElementById('identifierLabel');

        // Check if user is already logged in
        const token = localStorage.getItem('accessToken');
        if (token) {
            showDashboard();
        }

        // Login method toggle handlers
        emailBtn.addEventListener('click', () => {
            loginMethod = 'email';
            emailBtn.className = 'px-4 py-2 text-sm font-medium rounded-l-lg border bg-blue-600 text-white border-blue-600';
            employeeIdBtn.className = 'px-4 py-2 text-sm font-medium rounded-r-lg border-t border-r border-b bg-white text-gray-700 border-gray-300 hover:bg-gray-50';
            identifierLabel.textContent = 'Email address';
            identifierInput.type = 'email';
            identifierInput.placeholder = 'Enter your email address';
            identifierInput.value = '<EMAIL>';
        });

        employeeIdBtn.addEventListener('click', () => {
            loginMethod = 'employee_id';
            employeeIdBtn.className = 'px-4 py-2 text-sm font-medium rounded-r-lg border bg-blue-600 text-white border-blue-600';
            emailBtn.className = 'px-4 py-2 text-sm font-medium rounded-l-lg border-t border-l border-b bg-white text-gray-700 border-gray-300 hover:bg-gray-50';
            identifierLabel.textContent = 'Employee ID';
            identifierInput.type = 'text';
            identifierInput.placeholder = 'Enter your employee ID';
            identifierInput.value = 'EMP001';
        });

        // Login form handler
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const identifier = document.getElementById('identifier').value;
            const password = document.getElementById('password').value;

            setLoading(true);
            hideError();

            // Prepare login data based on method
            const loginData = { password };
            if (loginMethod === 'email') {
                loginData.email = identifier;
            } else {
                loginData.employee_id = identifier;
            }

            console.log('Sending login data:', loginData);

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData),
                });

                const data = await response.json();
                console.log('Login response:', data);

                if (data.success) {
                    currentUser = data.data.staff;
                    localStorage.setItem('accessToken', data.data.tokens.accessToken);
                    showDashboard();
                    showSuccess(`Welcome ${currentUser.first_name} ${currentUser.last_name}!`);
                } else {
                    showError(data.error || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Connection error. Please check if the server is running.');
            } finally {
                setLoading(false);
            }
        });

        // Logout handler
        logoutBtn.addEventListener('click', () => {
            localStorage.removeItem('accessToken');
            currentUser = null;
            showLogin();
        });

        function setLoading(loading) {
            if (loading) {
                loginText.textContent = 'Signing in...';
                loginSpinner.classList.remove('hidden');
                loginBtn.disabled = true;
            } else {
                loginText.textContent = 'Sign in';
                loginSpinner.classList.add('hidden');
                loginBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.querySelector('div').textContent = message;
            errorMessage.classList.remove('hidden');
        }

        function hideError() {
            errorMessage.classList.add('hidden');
        }

        function showSuccess(message) {
            // Simple success notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function showLogin() {
            loginPage.classList.remove('hidden');
            dashboardPage.classList.add('hidden');
        }

        function showDashboard() {
            loginPage.classList.add('hidden');
            dashboardPage.classList.remove('hidden');

            if (currentUser) {
                userName.textContent = `${currentUser.first_name} ${currentUser.last_name}`;
            }

            // Load dashboard data
            loadDashboardData();
        }

        async function loadDashboardData() {
            try {
                const token = localStorage.getItem('accessToken');
                const response = await fetch(`${API_BASE}/reports/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    updateDashboardStats(data.data);
                }
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            }
        }

        function updateDashboardStats(data) {
            if (data.staff) {
                document.getElementById('totalStaff').textContent = data.staff.total_staff || '10';
            }
            if (data.today) {
                document.getElementById('presentToday').textContent = data.today.total_present || '8';
                document.getElementById('lateToday').textContent = data.today.total_late || '2';
            }
            if (data.leaves) {
                document.getElementById('pendingLeaves').textContent = data.leaves.pending_approvals || '3';
            }
        }

        // Test server connection on page load
        async function testConnection() {
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    console.log('✅ Server connection successful');
                } else {
                    console.log('⚠️ Server responded but with error');
                }
            } catch (error) {
                console.log('❌ Server connection failed:', error.message);
            }
        }

        testConnection();
    </script>
</body>
</html>
