# Staff Attendance Management System - Setup Guide

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- Redis (v6 or higher)
- Git

## Installation Steps

### 1. Clone and Setup Project

```bash
# Navigate to project directory
cd "c:\Users\<USER>\OneDrive\Desktop\staff new"

# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 2. Database Setup

```bash
# Create PostgreSQL database
createdb staff_attendance

# Or using psql
psql -U postgres
CREATE DATABASE staff_attendance;
\q
```

### 3. Environment Configuration

```bash
# Copy server environment file
cd server
cp .env.example .env

# Edit .env file with your database credentials
# Update the following variables:
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=staff_attendance
# DB_USER=your_username
# DB_PASSWORD=your_password
# JWT_SECRET=your_super_secret_jwt_key_here
# ENCRYPTION_KEY=your_32_character_encryption_key

# Copy client environment file
cd ../client
cp .env.example .env
```

### 4. Database Seeding

```bash
# Run database migrations and seed data
cd ../server
npm run seed
```

This will create:
- Sample departments (HR, IT, Finance, Marketing, Operations)
- User roles (Admin, Manager, Employee, etc.)
- Sample staff members with login credentials
- Biometric data for testing

### 5. Start the Application

```bash
# Start both server and client (from root directory)
cd ..
npm run dev

# Or start individually:
# Server: npm run server:dev
# Client: npm run client:dev
```

### 6. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/docs

## Default Login Credentials

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

### HR Manager
- **Email**: <EMAIL>
- **Password**: password123

### IT Manager
- **Email**: <EMAIL>
- **Password**: password123

### Regular Employee
- **Employee ID**: EMP007
- **Password**: password123

## Features Available

### ✅ Completed Features
- User authentication with JWT
- Role-based access control
- Database models and relationships
- API endpoints for all modules
- Real-time updates with Socket.IO
- Biometric simulation system
- Basic frontend structure
- Dashboard with statistics

### 🚧 In Development
- Complete frontend modules
- Advanced reporting
- File upload functionality
- Email notifications
- Advanced biometric integration

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update profile
- `POST /api/auth/change-password` - Change password

### Staff Management
- `GET /api/staff` - List all staff
- `POST /api/staff` - Create new staff
- `GET /api/staff/:id` - Get staff by ID
- `PUT /api/staff/:id` - Update staff
- `DELETE /api/staff/:id` - Delete staff

### Attendance
- `POST /api/attendance/checkin` - Check in
- `POST /api/attendance/checkout` - Check out
- `GET /api/attendance` - Get attendance records
- `GET /api/attendance/today` - Today's attendance
- `GET /api/attendance/stats` - Attendance statistics

### Leave Management
- `GET /api/leave` - List leaves
- `POST /api/leave` - Apply for leave
- `PUT /api/leave/:id/approve` - Approve/reject leave
- `GET /api/leave/pending` - Pending leaves

### Biometric
- `POST /api/biometric/enroll` - Enroll fingerprint
- `POST /api/biometric/verify` - Verify fingerprint
- `GET /api/biometric/:staffId` - Get biometric data

### Reports
- `GET /api/reports/dashboard` - Dashboard statistics
- `GET /api/reports/attendance` - Attendance reports
- `GET /api/reports/leave` - Leave reports

## Troubleshooting

### Database Connection Issues
1. Ensure PostgreSQL is running
2. Check database credentials in .env file
3. Verify database exists

### Redis Connection Issues
1. Ensure Redis is running
2. Check Redis URL in .env file

### Port Conflicts
- Server runs on port 5000
- Client runs on port 3000
- Change ports in package.json if needed

### Permission Issues
- Ensure user has proper role and permissions
- Check role assignments in database

## Development

### Adding New Features
1. Create database models in `server/src/models/`
2. Add API routes in `server/src/routes/`
3. Create controllers in `server/src/controllers/`
4. Add frontend pages in `client/src/pages/`
5. Update navigation in `client/src/components/layouts/DashboardLayout.jsx`

### Testing
```bash
# Run server tests
cd server
npm test

# Run client tests
cd client
npm test
```

## Production Deployment

### Environment Variables
Set the following for production:
- `NODE_ENV=production`
- Database credentials
- JWT secrets
- CORS origins
- SSL certificates

### Build Process
```bash
# Build client
cd client
npm run build

# Start production server
cd ../server
npm start
```

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review API documentation
3. Check server logs in `server/logs/`
4. Verify database connections and data
