import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { useAuthStore } from '../../stores/authStore'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [loginMethod, setLoginMethod] = useState('email') // 'email' or 'employee_id'
  
  const navigate = useNavigate()
  const location = useLocation()
  const { login, isLoading } = useAuthStore()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm()

  const from = location.state?.from?.pathname || '/dashboard'

  const onSubmit = async (data) => {
    try {
      const credentials = loginMethod === 'email' 
        ? { email: data.identifier, password: data.password }
        : { employee_id: data.identifier, password: data.password }

      const result = await login(credentials)
      
      if (result.success) {
        navigate(from, { replace: true })
      } else {
        setError('root', { message: result.error })
      }
    } catch (error) {
      setError('root', { message: 'Login failed. Please try again.' })
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Sign in to your account</h2>
        <p className="mt-2 text-sm text-gray-600">
          Enter your credentials to access the attendance system
        </p>
      </div>

      {/* Login Method Toggle */}
      <div className="flex rounded-md shadow-sm" role="group">
        <button
          type="button"
          onClick={() => setLoginMethod('email')}
          className={`px-4 py-2 text-sm font-medium rounded-l-lg border ${
            loginMethod === 'email'
              ? 'bg-primary-600 text-white border-primary-600'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
        >
          Email
        </button>
        <button
          type="button"
          onClick={() => setLoginMethod('employee_id')}
          className={`px-4 py-2 text-sm font-medium rounded-r-lg border-t border-r border-b ${
            loginMethod === 'employee_id'
              ? 'bg-primary-600 text-white border-primary-600'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
        >
          Employee ID
        </button>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
        {/* Identifier Field */}
        <div>
          <label htmlFor="identifier" className="form-label">
            {loginMethod === 'email' ? 'Email address' : 'Employee ID'}
          </label>
          <input
            id="identifier"
            type={loginMethod === 'email' ? 'email' : 'text'}
            autoComplete={loginMethod === 'email' ? 'email' : 'username'}
            className={`input ${errors.identifier ? 'input-error' : ''}`}
            placeholder={
              loginMethod === 'email' 
                ? 'Enter your email address' 
                : 'Enter your employee ID'
            }
            {...register('identifier', {
              required: `${loginMethod === 'email' ? 'Email' : 'Employee ID'} is required`,
              ...(loginMethod === 'email' && {
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })
            })}
          />
          {errors.identifier && (
            <p className="form-error">{errors.identifier.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              className={`input pr-10 ${errors.password ? 'input-error' : ''}`}
              placeholder="Enter your password"
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 6,
                  message: 'Password must be at least 6 characters'
                }
              })}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="form-error">{errors.password.message}</p>
          )}
        </div>

        {/* Remember Me */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
              Remember me
            </label>
          </div>

          <div className="text-sm">
            <a href="#" className="font-medium text-primary-600 hover:text-primary-500">
              Forgot your password?
            </a>
          </div>
        </div>

        {/* Error Message */}
        {errors.root && (
          <div className="rounded-md bg-danger-50 p-4">
            <div className="text-sm text-danger-700">{errors.root.message}</div>
          </div>
        )}

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            disabled={isLoading}
            className="btn btn-primary btn-md w-full"
          >
            {isLoading ? (
              <LoadingSpinner size="sm" color="white" />
            ) : (
              'Sign in'
            )}
          </button>
        </div>
      </form>

      {/* Demo Credentials */}
      <div className="mt-6 p-4 bg-gray-50 rounded-md">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Demo Credentials:</h3>
        <div className="text-xs text-gray-600 space-y-1">
          <div><strong>Admin:</strong> <EMAIL> / admin123</div>
          <div><strong>HR Manager:</strong> <EMAIL> / password123</div>
          <div><strong>Employee:</strong> EMP003 / password123</div>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
