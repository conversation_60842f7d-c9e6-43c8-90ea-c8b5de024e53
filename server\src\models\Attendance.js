const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Attendance = sequelize.define('Attendance', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  staff_id: {
    type: DataTypes.STRING(20),
    allowNull: false,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  time_in: {
    type: DataTypes.TIME,
    allowNull: true
  },
  time_out: {
    type: DataTypes.TIME,
    allowNull: true
  },
  break_start: {
    type: DataTypes.TIME,
    allowNull: true
  },
  break_end: {
    type: DataTypes.TIME,
    allowNull: true
  },
  total_hours: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: true
  },
  break_duration: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: true,
    defaultValue: 0
  },
  overtime_hours: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: true,
    defaultValue: 0
  },
  status: {
    type: DataTypes.ENUM('present', 'absent', 'late', 'half_day', 'on_leave'),
    allowNull: false,
    defaultValue: 'present'
  },
  late_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0
  },
  early_departure_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Location where attendance was marked'
  },
  device_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Biometric device ID used for attendance'
  },
  verification_method: {
    type: DataTypes.ENUM('fingerprint', 'manual', 'card', 'face'),
    allowNull: false,
    defaultValue: 'fingerprint'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  approved_by: {
    type: DataTypes.STRING(20),
    allowNull: true,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  is_holiday: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_weekend: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'attendance',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['staff_id', 'date']
    },
    {
      fields: ['date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['staff_id', 'date', 'status']
    }
  ],
  hooks: {
    beforeSave: (attendance) => {
      // Calculate total hours if both time_in and time_out are present
      if (attendance.time_in && attendance.time_out) {
        const timeIn = new Date(`1970-01-01T${attendance.time_in}`);
        const timeOut = new Date(`1970-01-01T${attendance.time_out}`);
        const diffMs = timeOut - timeIn;
        const diffHours = diffMs / (1000 * 60 * 60);
        
        // Subtract break duration
        const totalHours = diffHours - (attendance.break_duration || 0);
        attendance.total_hours = Math.max(0, totalHours);
        
        // Calculate overtime (assuming 8 hours is standard)
        const standardHours = 8;
        attendance.overtime_hours = Math.max(0, totalHours - standardHours);
      }
      
      // Set weekend flag
      const date = new Date(attendance.date);
      const dayOfWeek = date.getDay();
      attendance.is_weekend = dayOfWeek === 0 || dayOfWeek === 6; // Sunday = 0, Saturday = 6
    }
  }
});

// Instance methods
Attendance.prototype.calculateWorkingHours = function() {
  if (!this.time_in || !this.time_out) return 0;
  
  const timeIn = new Date(`1970-01-01T${this.time_in}`);
  const timeOut = new Date(`1970-01-01T${this.time_out}`);
  const diffMs = timeOut - timeIn;
  const diffHours = diffMs / (1000 * 60 * 60);
  
  return Math.max(0, diffHours - (this.break_duration || 0));
};

Attendance.prototype.isLate = function(shiftStart) {
  if (!this.time_in || !shiftStart) return false;
  
  const timeIn = new Date(`1970-01-01T${this.time_in}`);
  const shiftStartTime = new Date(`1970-01-01T${shiftStart}`);
  
  return timeIn > shiftStartTime;
};

Attendance.prototype.isEarlyDeparture = function(shiftEnd) {
  if (!this.time_out || !shiftEnd) return false;
  
  const timeOut = new Date(`1970-01-01T${this.time_out}`);
  const shiftEndTime = new Date(`1970-01-01T${shiftEnd}`);
  
  return timeOut < shiftEndTime;
};

module.exports = Attendance;
