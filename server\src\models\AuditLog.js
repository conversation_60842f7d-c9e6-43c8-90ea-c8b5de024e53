const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AuditLog = sequelize.define('AuditLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  staff_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  action: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Action performed (e.g., login, logout, create_staff, etc.)'
  },
  resource: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Resource affected (e.g., staff, attendance, leave, etc.)'
  },
  resource_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'ID of the affected resource'
  },
  old_values: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Previous values before the change'
  },
  new_values: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'New values after the change'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: 'IP address of the user'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Browser/device information'
  },
  session_id: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('success', 'failure', 'warning'),
    allowNull: false,
    defaultValue: 'success'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional context information'
  },
  severity: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    allowNull: false,
    defaultValue: 'low'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'audit_logs',
  timestamps: false,
  indexes: [
    {
      fields: ['staff_id']
    },
    {
      fields: ['action']
    },
    {
      fields: ['resource']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['status']
    },
    {
      fields: ['severity']
    },
    {
      fields: ['staff_id', 'action', 'created_at']
    }
  ]
});

// Static methods for logging different types of actions
AuditLog.logLogin = async function(staffId, ipAddress, userAgent, success = true) {
  return await this.create({
    staff_id: staffId,
    action: 'login',
    resource: 'authentication',
    ip_address: ipAddress,
    user_agent: userAgent,
    status: success ? 'success' : 'failure',
    severity: success ? 'low' : 'medium'
  });
};

AuditLog.logLogout = async function(staffId, ipAddress) {
  return await this.create({
    staff_id: staffId,
    action: 'logout',
    resource: 'authentication',
    ip_address: ipAddress,
    status: 'success',
    severity: 'low'
  });
};

AuditLog.logAttendance = async function(staffId, action, attendanceData, ipAddress) {
  return await this.create({
    staff_id: staffId,
    action: action, // 'checkin', 'checkout', 'modify_attendance'
    resource: 'attendance',
    resource_id: attendanceData.id?.toString(),
    new_values: attendanceData,
    ip_address: ipAddress,
    status: 'success',
    severity: 'low'
  });
};

AuditLog.logLeaveAction = async function(staffId, action, leaveData, oldValues = null, ipAddress = null) {
  return await this.create({
    staff_id: staffId,
    action: action, // 'apply_leave', 'approve_leave', 'reject_leave'
    resource: 'leave',
    resource_id: leaveData.id?.toString(),
    old_values: oldValues,
    new_values: leaveData,
    ip_address: ipAddress,
    status: 'success',
    severity: action.includes('approve') || action.includes('reject') ? 'medium' : 'low'
  });
};

AuditLog.logStaffAction = async function(staffId, action, targetStaffData, oldValues = null, ipAddress = null) {
  return await this.create({
    staff_id: staffId,
    action: action, // 'create_staff', 'update_staff', 'delete_staff'
    resource: 'staff',
    resource_id: targetStaffData.staff_id,
    old_values: oldValues,
    new_values: targetStaffData,
    ip_address: ipAddress,
    status: 'success',
    severity: action === 'delete_staff' ? 'high' : 'medium'
  });
};

AuditLog.logBiometricAction = async function(staffId, action, biometricData, ipAddress = null) {
  return await this.create({
    staff_id: staffId,
    action: action, // 'enroll_fingerprint', 'verify_fingerprint', 'update_biometric'
    resource: 'biometric',
    resource_id: biometricData.id?.toString(),
    new_values: {
      finger_position: biometricData.finger_position,
      template_quality: biometricData.template_quality,
      device_id: biometricData.device_id
    },
    ip_address: ipAddress,
    status: 'success',
    severity: 'medium'
  });
};

AuditLog.logSecurityEvent = async function(staffId, action, details, ipAddress, severity = 'high') {
  return await this.create({
    staff_id: staffId,
    action: action,
    resource: 'security',
    metadata: details,
    ip_address: ipAddress,
    status: 'warning',
    severity: severity
  });
};

AuditLog.logSystemEvent = async function(action, details, severity = 'low') {
  return await this.create({
    action: action,
    resource: 'system',
    metadata: details,
    status: 'success',
    severity: severity
  });
};

// Instance methods
AuditLog.prototype.getFormattedAction = function() {
  return this.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

AuditLog.prototype.getChangesSummary = function() {
  if (!this.old_values || !this.new_values) return null;
  
  const changes = {};
  const oldVals = this.old_values;
  const newVals = this.new_values;
  
  for (const key in newVals) {
    if (oldVals[key] !== newVals[key]) {
      changes[key] = {
        from: oldVals[key],
        to: newVals[key]
      };
    }
  }
  
  return Object.keys(changes).length > 0 ? changes : null;
};

module.exports = AuditLog;
