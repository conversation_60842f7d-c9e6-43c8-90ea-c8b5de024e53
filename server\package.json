{"name": "staff-attendance-server", "version": "1.0.0", "description": "Backend API for Staff Attendance Management System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "sequelize": "^6.35.0", "redis": "^4.6.10", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "crypto": "^1.0.1", "node-cron": "^3.0.3", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "keywords": ["attendance", "api", "biometric", "nodejs", "express"], "author": "Staff Management Team", "license": "MIT"}