const { Staff, Attendance, Leave, Department, sequelize } = require('../models');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

class ReportsController {
  // Get attendance report
  static async getAttendanceReport(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { start_date, end_date, staff_id, department_id } = req.query;
      const whereClause = {};

      // Date range filter
      if (start_date && end_date) {
        whereClause.date = {
          [Op.between]: [start_date, end_date]
        };
      } else {
        // Default to current month
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        whereClause.date = {
          [Op.between]: [startOfMonth.toISOString().split('T')[0], endOfMonth.toISOString().split('T')[0]]
        };
      }

      // Staff filter
      if (staff_id) {
        whereClause.staff_id = staff_id;
      }

      const includeClause = [{
        model: Staff,
        as: 'staff',
        attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
        include: [{
          model: Department,
          as: 'department',
          attributes: ['id', 'name', 'code'],
          where: department_id ? { id: department_id } : undefined
        }]
      }];

      const attendance = await Attendance.findAll({
        where: whereClause,
        include: includeClause,
        order: [['date', 'DESC'], ['time_in', 'ASC']]
      });

      // Calculate summary statistics
      const totalRecords = attendance.length;
      const presentCount = attendance.filter(a => a.status === 'present').length;
      const lateCount = attendance.filter(a => a.status === 'late').length;
      const absentCount = attendance.filter(a => a.status === 'absent').length;
      
      const totalHours = attendance.reduce((sum, a) => sum + (parseFloat(a.total_hours) || 0), 0);
      const avgHours = totalRecords > 0 ? (totalHours / totalRecords).toFixed(2) : 0;

      // Department-wise summary
      const departmentSummary = {};
      attendance.forEach(a => {
        const deptName = a.staff?.department?.name || 'Unknown';
        if (!departmentSummary[deptName]) {
          departmentSummary[deptName] = {
            total: 0,
            present: 0,
            late: 0,
            absent: 0,
            total_hours: 0
          };
        }
        departmentSummary[deptName].total++;
        departmentSummary[deptName][a.status]++;
        departmentSummary[deptName].total_hours += parseFloat(a.total_hours) || 0;
      });

      res.json({
        success: true,
        data: {
          attendance,
          summary: {
            total_records: totalRecords,
            present_count: presentCount,
            late_count: lateCount,
            absent_count: absentCount,
            attendance_rate: totalRecords > 0 ? ((presentCount + lateCount) / totalRecords * 100).toFixed(2) : 0,
            punctuality_rate: (presentCount + lateCount) > 0 ? (presentCount / (presentCount + lateCount) * 100).toFixed(2) : 0,
            total_hours: totalHours.toFixed(2),
            average_hours: avgHours
          },
          department_summary: departmentSummary
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get leave report
  static async getLeaveReport(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { start_date, end_date, staff_id, department_id } = req.query;
      const whereClause = {};

      // Date range filter
      if (start_date && end_date) {
        whereClause[Op.or] = [
          {
            start_date: {
              [Op.between]: [start_date, end_date]
            }
          },
          {
            end_date: {
              [Op.between]: [start_date, end_date]
            }
          }
        ];
      }

      // Staff filter
      if (staff_id) {
        whereClause.staff_id = staff_id;
      }

      const includeClause = [
        {
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
          include: [{
            model: Department,
            as: 'department',
            attributes: ['id', 'name', 'code'],
            where: department_id ? { id: department_id } : undefined
          }]
        },
        {
          model: Staff,
          as: 'approver',
          attributes: ['staff_id', 'first_name', 'last_name'],
          required: false
        }
      ];

      const leaves = await Leave.findAll({
        where: whereClause,
        include: includeClause,
        order: [['applied_date', 'DESC']]
      });

      // Calculate summary statistics
      const totalApplications = leaves.length;
      const approvedCount = leaves.filter(l => l.status === 'approved').length;
      const rejectedCount = leaves.filter(l => l.status === 'rejected').length;
      const pendingCount = leaves.filter(l => l.status === 'pending').length;
      
      const totalDaysApproved = leaves
        .filter(l => l.status === 'approved')
        .reduce((sum, l) => sum + l.total_days, 0);

      // Leave type summary
      const typeStats = {};
      leaves.forEach(l => {
        if (!typeStats[l.leave_type]) {
          typeStats[l.leave_type] = {
            total: 0,
            approved: 0,
            rejected: 0,
            pending: 0,
            total_days: 0
          };
        }
        typeStats[l.leave_type].total++;
        typeStats[l.leave_type][l.status]++;
        if (l.status === 'approved') {
          typeStats[l.leave_type].total_days += l.total_days;
        }
      });

      res.json({
        success: true,
        data: {
          leaves,
          summary: {
            total_applications: totalApplications,
            approved_count: approvedCount,
            rejected_count: rejectedCount,
            pending_count: pendingCount,
            approval_rate: totalApplications > 0 ? (approvedCount / totalApplications * 100).toFixed(2) : 0,
            total_days_approved: totalDaysApproved
          },
          leave_type_stats: typeStats
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get staff report
  static async getStaffReport(req, res) {
    try {
      const { department_id } = req.query;
      const whereClause = { status: 'active' };

      if (department_id) {
        whereClause.department_id = department_id;
      }

      const staff = await Staff.findAll({
        where: whereClause,
        include: ['department', 'role', 'biometric'],
        order: [['first_name', 'ASC']]
      });

      // Calculate statistics
      const totalStaff = staff.length;
      const biometricEnrolled = staff.filter(s => s.is_biometric_enrolled).length;
      
      // Department-wise count
      const departmentStats = {};
      staff.forEach(s => {
        const deptName = s.department?.name || 'Unknown';
        departmentStats[deptName] = (departmentStats[deptName] || 0) + 1;
      });

      // Role-wise count
      const roleStats = {};
      staff.forEach(s => {
        const roleName = s.role?.name || 'Unknown';
        roleStats[roleName] = (roleStats[roleName] || 0) + 1;
      });

      res.json({
        success: true,
        data: {
          staff,
          summary: {
            total_staff: totalStaff,
            biometric_enrolled: biometricEnrolled,
            enrollment_rate: totalStaff > 0 ? (biometricEnrolled / totalStaff * 100).toFixed(2) : 0
          },
          department_stats: departmentStats,
          role_stats: roleStats
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get dashboard statistics
  static async getDashboardStats(req, res) {
    try {
      const today = new Date().toISOString().split('T')[0];
      const thisMonth = new Date();
      const startOfMonth = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1);
      const endOfMonth = new Date(thisMonth.getFullYear(), thisMonth.getMonth() + 1, 0);

      // Today's attendance
      const todayAttendance = await Attendance.findAll({
        where: { date: today },
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name']
        }]
      });

      const totalPresent = todayAttendance.filter(a => a.time_in).length;
      const totalLate = todayAttendance.filter(a => a.status === 'late').length;
      const totalCheckedOut = todayAttendance.filter(a => a.time_out).length;

      // Staff statistics
      const totalStaff = await Staff.count({ where: { status: 'active' } });
      const biometricEnrolled = await Staff.count({ 
        where: { status: 'active', is_biometric_enrolled: true } 
      });

      // Leave statistics
      const pendingLeaves = await Leave.count({ where: { status: 'pending' } });
      const monthlyLeaves = await Leave.count({
        where: {
          status: 'approved',
          start_date: {
            [Op.between]: [startOfMonth.toISOString().split('T')[0], endOfMonth.toISOString().split('T')[0]]
          }
        }
      });

      // Monthly attendance trends
      const monthlyAttendance = await Attendance.findAll({
        attributes: [
          [sequelize.fn('DATE', sequelize.col('date')), 'date'],
          [sequelize.fn('COUNT', '*'), 'total'],
          [sequelize.fn('SUM', sequelize.literal("CASE WHEN status = 'present' THEN 1 ELSE 0 END")), 'present'],
          [sequelize.fn('SUM', sequelize.literal("CASE WHEN status = 'late' THEN 1 ELSE 0 END")), 'late']
        ],
        where: {
          date: {
            [Op.between]: [startOfMonth.toISOString().split('T')[0], endOfMonth.toISOString().split('T')[0]]
          }
        },
        group: [sequelize.fn('DATE', sequelize.col('date'))],
        order: [[sequelize.fn('DATE', sequelize.col('date')), 'ASC']]
      });

      res.json({
        success: true,
        data: {
          today: {
            total_present: totalPresent,
            total_late: totalLate,
            total_checked_out: totalCheckedOut,
            still_in_office: totalPresent - totalCheckedOut,
            attendance_rate: totalStaff > 0 ? (totalPresent / totalStaff * 100).toFixed(2) : 0
          },
          staff: {
            total_staff: totalStaff,
            biometric_enrolled: biometricEnrolled,
            enrollment_rate: totalStaff > 0 ? (biometricEnrolled / totalStaff * 100).toFixed(2) : 0
          },
          leaves: {
            pending_approvals: pendingLeaves,
            monthly_approved: monthlyLeaves
          },
          trends: {
            monthly_attendance: monthlyAttendance
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Export attendance report (CSV format)
  static async exportAttendanceReport(req, res) {
    try {
      // Implementation for CSV export would go here
      // For now, return JSON with export flag
      const reportData = await ReportsController.getAttendanceReport(req, { json: () => {} });
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=attendance-report.json');
      
      res.json({
        success: true,
        message: 'Export functionality to be implemented',
        data: reportData
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Export leave report (CSV format)
  static async exportLeaveReport(req, res) {
    try {
      // Implementation for CSV export would go here
      // For now, return JSON with export flag
      const reportData = await ReportsController.getLeaveReport(req, { json: () => {} });
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=leave-report.json');
      
      res.json({
        success: true,
        message: 'Export functionality to be implemented',
        data: reportData
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = ReportsController;
