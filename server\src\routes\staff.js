const express = require('express');
const { body, param, query } = require('express-validator');
const StaffController = require('../controllers/staffController');
const { authenticate, authorize, requireAdmin, requireManager } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation rules
const createStaffValidation = [
  body('employee_id')
    .notEmpty()
    .withMessage('Employee ID is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Employee ID must be between 1 and 50 characters'),
  body('first_name')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('First name must be between 2 and 100 characters'),
  body('last_name')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Last name must be between 2 and 100 characters'),
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('department_id')
    .isInt({ min: 1 })
    .withMessage('Valid department ID is required'),
  body('role_id')
    .isInt({ min: 1 })
    .withMessage('Valid role ID is required'),
  body('position')
    .notEmpty()
    .withMessage('Position is required'),
  body('hire_date')
    .isISO8601()
    .withMessage('Valid hire date is required'),
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),
  body('salary')
    .optional()
    .isDecimal()
    .withMessage('Salary must be a valid decimal number'),
  body('shift_start')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Shift start time must be in HH:MM:SS format'),
  body('shift_end')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Shift end time must be in HH:MM:SS format')
];

const updateStaffValidation = [
  body('employee_id')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Employee ID must be between 1 and 50 characters'),
  body('first_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('First name must be between 2 and 100 characters'),
  body('last_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Last name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('department_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid department ID is required'),
  body('role_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid role ID is required'),
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),
  body('salary')
    .optional()
    .isDecimal()
    .withMessage('Salary must be a valid decimal number'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'terminated'])
    .withMessage('Status must be active, inactive, or terminated')
];

const staffIdValidation = [
  param('id')
    .notEmpty()
    .withMessage('Staff ID is required')
];

const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sortBy')
    .optional()
    .isIn(['first_name', 'last_name', 'email', 'employee_id', 'hire_date', 'created_at'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

// Routes
router.get('/', 
  queryValidation, 
  authorize('view_staff', 'manage_staff'), 
  StaffController.getAllStaff
);

router.get('/stats', 
  requireManager, 
  StaffController.getStaffStats
);

router.get('/:id', 
  staffIdValidation, 
  authorize('view_staff', 'manage_staff'), 
  StaffController.getStaffById
);

router.post('/', 
  createStaffValidation, 
  authorize('manage_staff'), 
  StaffController.createStaff
);

router.put('/:id', 
  staffIdValidation, 
  updateStaffValidation, 
  authorize('manage_staff'), 
  StaffController.updateStaff
);

router.delete('/:id', 
  staffIdValidation, 
  requireAdmin, 
  StaffController.deleteStaff
);

module.exports = router;
