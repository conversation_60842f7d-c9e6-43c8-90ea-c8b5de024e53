const JWTUtils = require('../utils/jwt');
const { Staff } = require('../models');
const logger = require('../utils/logger');

// Middleware to verify JWT token
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. No token provided.'
      });
    }

    const token = JWTUtils.extractTokenFromHeader(authHeader);
    
    // Check if token is blacklisted
    const isBlacklisted = await JWTUtils.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        error: 'Token has been revoked.'
      });
    }

    // Verify token
    const decoded = JWTUtils.verifyAccessToken(token);
    
    // Get staff data from database
    const staff = await Staff.findByPk(decoded.staff_id, {
      include: ['role', 'department', 'biometric']
    });

    if (!staff) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token. Staff not found.'
      });
    }

    if (staff.status !== 'active') {
      return res.status(401).json({
        success: false,
        error: 'Account is inactive.'
      });
    }

    // Add staff info to request
    req.user = staff;
    req.token = token;
    
    next();
  } catch (error) {
    logger.logSecurity('authentication_failed', { error: error.message }, req);
    
    return res.status(401).json({
      success: false,
      error: 'Invalid token.'
    });
  }
};

// Middleware to check if user has specific permission
const authorize = (...permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.'
      });
    }

    const userPermissions = req.user.role?.getPermissionsList() || [];
    const hasPermission = permissions.some(permission => 
      userPermissions.includes(permission) || req.user.role?.is_admin
    );

    if (!hasPermission) {
      logger.logSecurity('authorization_failed', {
        staff_id: req.user.staff_id,
        required_permissions: permissions,
        user_permissions: userPermissions
      }, req);

      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions.'
      });
    }

    next();
  };
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  if (!req.user.role?.is_admin) {
    logger.logSecurity('admin_access_denied', {
      staff_id: req.user.staff_id,
      role: req.user.role?.name
    }, req);

    return res.status(403).json({
      success: false,
      error: 'Admin access required.'
    });
  }

  next();
};

// Middleware to check if user is manager or admin
const requireManager = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  if (!req.user.role?.is_manager && !req.user.role?.is_admin) {
    logger.logSecurity('manager_access_denied', {
      staff_id: req.user.staff_id,
      role: req.user.role?.name
    }, req);

    return res.status(403).json({
      success: false,
      error: 'Manager access required.'
    });
  }

  next();
};

// Middleware to check if user can access their own data or is admin/manager
const requireOwnershipOrManager = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  const targetStaffId = req.params.staffId || req.params.id || req.body.staff_id;
  const isOwner = req.user.staff_id === targetStaffId;
  const isManager = req.user.role?.is_manager || req.user.role?.is_admin;

  if (!isOwner && !isManager) {
    logger.logSecurity('ownership_access_denied', {
      staff_id: req.user.staff_id,
      target_staff_id: targetStaffId,
      role: req.user.role?.name
    }, req);

    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only access your own data.'
    });
  }

  next();
};

// Middleware to validate biometric enrollment
const requireBiometricEnrollment = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  if (!req.user.is_biometric_enrolled) {
    return res.status(403).json({
      success: false,
      error: 'Biometric enrollment required for this action.'
    });
  }

  next();
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return next();
    }

    const token = JWTUtils.extractTokenFromHeader(authHeader);
    
    // Check if token is blacklisted
    const isBlacklisted = await JWTUtils.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return next();
    }

    // Verify token
    const decoded = JWTUtils.verifyAccessToken(token);
    
    // Get staff data from database
    const staff = await Staff.findByPk(decoded.staff_id, {
      include: ['role', 'department', 'biometric']
    });

    if (staff && staff.status === 'active') {
      req.user = staff;
      req.token = token;
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

module.exports = {
  authenticate,
  authorize,
  requireAdmin,
  requireManager,
  requireOwnershipOrManager,
  requireBiometricEnrollment,
  optionalAuth
};
