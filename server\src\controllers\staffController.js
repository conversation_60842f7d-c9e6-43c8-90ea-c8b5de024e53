const { Staff, Department, Role, AuditLog } = require('../models');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

class StaffController {
  // Get all staff with pagination and filtering
  static async getAllStaff(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        department,
        role,
        status = 'active',
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Add filters
      if (status) {
        whereClause.status = status;
      }

      if (search) {
        whereClause[Op.or] = [
          { first_name: { [Op.iLike]: `%${search}%` } },
          { last_name: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { employee_id: { [Op.iLike]: `%${search}%` } }
        ];
      }

      const includeClause = [
        {
          model: Department,
          as: 'department',
          where: department ? { id: department } : undefined
        },
        {
          model: Role,
          as: 'role',
          where: role ? { id: role } : undefined
        }
      ];

      const { count, rows: staff } = await Staff.findAndCountAll({
        where: whereClause,
        include: includeClause,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true
      });

      res.json({
        success: true,
        data: {
          staff,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get staff by ID
  static async getStaffById(req, res) {
    try {
      const { id } = req.params;

      const staff = await Staff.findByPk(id, {
        include: ['department', 'role', 'biometric']
      });

      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      res.json({
        success: true,
        data: staff
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Create new staff
  static async createStaff(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      // Generate staff ID
      const timestamp = Date.now().toString(36);
      const random = Math.random().toString(36).substr(2, 5);
      const staff_id = `STF-${timestamp}-${random}`.toUpperCase();

      const staffData = {
        ...req.body,
        staff_id
      };

      const staff = await Staff.create(staffData);

      // Load associations
      await staff.reload({
        include: ['department', 'role']
      });

      // Log creation
      await AuditLog.logStaffAction(
        req.user.staff_id,
        'create_staff',
        staff.toJSON(),
        null,
        req.ip
      );

      logger.info(`Staff ${staff.staff_id} created by ${req.user.staff_id}`);

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('staff_created', {
        staff: staff.toJSON(),
        created_by: req.user.staff_id
      });

      res.status(201).json({
        success: true,
        message: 'Staff created successfully',
        data: staff
      });

    } catch (error) {
      logger.logError(error, req);
      
      if (error.name === 'SequelizeUniqueConstraintError') {
        const field = error.errors[0]?.path;
        return res.status(400).json({
          success: false,
          error: `${field} already exists`
        });
      }

      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Update staff
  static async updateStaff(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const staff = await Staff.findByPk(id);

      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      const oldValues = staff.toJSON();
      
      // Remove fields that shouldn't be updated directly
      const { staff_id, password, ...updateData } = req.body;

      await staff.update(updateData);

      // Reload with associations
      await staff.reload({
        include: ['department', 'role']
      });

      // Log update
      await AuditLog.logStaffAction(
        req.user.staff_id,
        'update_staff',
        staff.toJSON(),
        oldValues,
        req.ip
      );

      logger.info(`Staff ${staff.staff_id} updated by ${req.user.staff_id}`);

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('staff_updated', {
        staff: staff.toJSON(),
        updated_by: req.user.staff_id
      });

      res.json({
        success: true,
        message: 'Staff updated successfully',
        data: staff
      });

    } catch (error) {
      logger.logError(error, req);
      
      if (error.name === 'SequelizeUniqueConstraintError') {
        const field = error.errors[0]?.path;
        return res.status(400).json({
          success: false,
          error: `${field} already exists`
        });
      }

      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Delete staff (soft delete by setting status to terminated)
  static async deleteStaff(req, res) {
    try {
      const { id } = req.params;
      const staff = await Staff.findByPk(id);

      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      const oldValues = staff.toJSON();
      
      // Soft delete by updating status
      await staff.update({ status: 'terminated' });

      // Log deletion
      await AuditLog.logStaffAction(
        req.user.staff_id,
        'delete_staff',
        staff.toJSON(),
        oldValues,
        req.ip
      );

      logger.info(`Staff ${staff.staff_id} deleted by ${req.user.staff_id}`);

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('staff_deleted', {
        staff_id: staff.staff_id,
        deleted_by: req.user.staff_id
      });

      res.json({
        success: true,
        message: 'Staff deleted successfully'
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get staff statistics
  static async getStaffStats(req, res) {
    try {
      const totalStaff = await Staff.count();
      const activeStaff = await Staff.count({ where: { status: 'active' } });
      const inactiveStaff = await Staff.count({ where: { status: 'inactive' } });
      const terminatedStaff = await Staff.count({ where: { status: 'terminated' } });
      
      const biometricEnrolled = await Staff.count({ 
        where: { is_biometric_enrolled: true } 
      });

      // Department-wise count
      const departmentStats = await Staff.findAll({
        attributes: [
          'department_id',
          [Staff.sequelize.fn('COUNT', '*'), 'count']
        ],
        include: [{
          model: Department,
          as: 'department',
          attributes: ['name', 'code']
        }],
        group: ['department_id', 'department.id'],
        where: { status: 'active' }
      });

      // Role-wise count
      const roleStats = await Staff.findAll({
        attributes: [
          'role_id',
          [Staff.sequelize.fn('COUNT', '*'), 'count']
        ],
        include: [{
          model: Role,
          as: 'role',
          attributes: ['name']
        }],
        group: ['role_id', 'role.id'],
        where: { status: 'active' }
      });

      res.json({
        success: true,
        data: {
          overview: {
            total: totalStaff,
            active: activeStaff,
            inactive: inactiveStaff,
            terminated: terminatedStaff,
            biometric_enrolled: biometricEnrolled,
            biometric_enrollment_rate: totalStaff > 0 ? 
              ((biometricEnrolled / totalStaff) * 100).toFixed(2) : 0
          },
          by_department: departmentStats,
          by_role: roleStats
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = StaffController;
