const express = require('express');
const { body, param, query } = require('express-validator');
const LeaveController = require('../controllers/leaveController');
const { authenticate, authorize, requireOwnershipOrManager } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation rules
const applyLeaveValidation = [
  body('leave_type')
    .isIn(['annual', 'sick', 'maternity', 'paternity', 'emergency', 'bereavement', 'study', 'unpaid', 'compensatory', 'other'])
    .withMessage('Invalid leave type'),
  body('start_date')
    .isISO8601()
    .withMessage('Valid start date is required'),
  body('end_date')
    .isISO8601()
    .withMessage('Valid end date is required'),
  body('reason')
    .notEmpty()
    .withMessage('Reason is required')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Reason must be between 10 and 1000 characters'),
  body('is_half_day')
    .optional()
    .isBoolean()
    .withMessage('Half day must be a boolean value'),
  body('half_day_period')
    .optional()
    .isIn(['morning', 'afternoon'])
    .withMessage('Half day period must be morning or afternoon'),
  body('emergency_contact')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Emergency contact must not exceed 255 characters'),
  body('substitute_staff_id')
    .optional()
    .notEmpty()
    .withMessage('Substitute staff ID cannot be empty'),
  body('handover_notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Handover notes must not exceed 1000 characters')
];

const updateLeaveValidation = [
  body('leave_type')
    .optional()
    .isIn(['annual', 'sick', 'maternity', 'paternity', 'emergency', 'bereavement', 'study', 'unpaid', 'compensatory', 'other'])
    .withMessage('Invalid leave type'),
  body('start_date')
    .optional()
    .isISO8601()
    .withMessage('Valid start date is required'),
  body('end_date')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required'),
  body('reason')
    .optional()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Reason must be between 10 and 1000 characters'),
  body('is_half_day')
    .optional()
    .isBoolean()
    .withMessage('Half day must be a boolean value'),
  body('half_day_period')
    .optional()
    .isIn(['morning', 'afternoon'])
    .withMessage('Half day period must be morning or afternoon')
];

const approveLeaveValidation = [
  body('status')
    .isIn(['approved', 'rejected'])
    .withMessage('Status must be approved or rejected'),
  body('rejection_reason')
    .if(body('status').equals('rejected'))
    .notEmpty()
    .withMessage('Rejection reason is required when rejecting leave')
    .isLength({ min: 10, max: 500 })
    .withMessage('Rejection reason must be between 10 and 500 characters')
];

const leaveIdValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Valid leave ID is required')
];

const queryValidation = [
  query('staff_id')
    .optional()
    .notEmpty()
    .withMessage('Staff ID cannot be empty'),
  query('status')
    .optional()
    .isIn(['pending', 'approved', 'rejected', 'cancelled'])
    .withMessage('Invalid status'),
  query('leave_type')
    .optional()
    .isIn(['annual', 'sick', 'maternity', 'paternity', 'emergency', 'bereavement', 'study', 'unpaid', 'compensatory', 'other'])
    .withMessage('Invalid leave type'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Valid start date is required'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

// Routes
router.get('/', 
  queryValidation, 
  authorize('view_leave', 'manage_leave'), 
  LeaveController.getAllLeaves
);

router.get('/pending', 
  authorize('approve_leave', 'manage_leave'), 
  LeaveController.getPendingLeaves
);

router.get('/stats', 
  authorize('view_reports', 'manage_leave'), 
  LeaveController.getLeaveStats
);

router.get('/:id', 
  leaveIdValidation, 
  requireOwnershipOrManager, 
  LeaveController.getLeaveById
);

router.post('/', 
  applyLeaveValidation, 
  authorize('apply_leave'), 
  LeaveController.applyLeave
);

router.put('/:id', 
  leaveIdValidation, 
  updateLeaveValidation, 
  requireOwnershipOrManager, 
  LeaveController.updateLeave
);

router.put('/:id/approve', 
  leaveIdValidation, 
  approveLeaveValidation, 
  authorize('approve_leave'), 
  LeaveController.approveLeave
);

router.delete('/:id', 
  leaveIdValidation, 
  requireOwnershipOrManager, 
  LeaveController.cancelLeave
);

module.exports = router;
