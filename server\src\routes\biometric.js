const express = require('express');
const { body, param } = require('express-validator');
const BiometricController = require('../controllers/biometricController');
const { authenticate, authorize, requireAdmin, requireOwnershipOrManager } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation rules
const enrollFingerprintValidation = [
  body('staff_id')
    .notEmpty()
    .withMessage('Staff ID is required'),
  body('fingerprint_template')
    .notEmpty()
    .withMessage('Fingerprint template is required')
    .isLength({ min: 10 })
    .withMessage('Fingerprint template must be at least 10 characters'),
  body('template_quality')
    .isInt({ min: 0, max: 100 })
    .withMessage('Template quality must be between 0 and 100'),
  body('finger_position')
    .optional()
    .isIn([
      'right_thumb', 'right_index', 'right_middle', 'right_ring', 'right_little',
      'left_thumb', 'left_index', 'left_middle', 'left_ring', 'left_little'
    ])
    .withMessage('Invalid finger position'),
  body('device_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Device ID must not exceed 100 characters')
];

const verifyFingerprintValidation = [
  body('staff_id')
    .notEmpty()
    .withMessage('Staff ID is required'),
  body('fingerprint_template')
    .notEmpty()
    .withMessage('Fingerprint template is required')
    .isLength({ min: 10 })
    .withMessage('Fingerprint template must be at least 10 characters'),
  body('device_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Device ID must not exceed 100 characters'),
  body('action')
    .optional()
    .isIn(['attendance', 'access_control', 'authentication'])
    .withMessage('Invalid action type')
];

const updateBiometricValidation = [
  body('fingerprint_template')
    .notEmpty()
    .withMessage('Fingerprint template is required')
    .isLength({ min: 10 })
    .withMessage('Fingerprint template must be at least 10 characters'),
  body('template_quality')
    .isInt({ min: 0, max: 100 })
    .withMessage('Template quality must be between 0 and 100'),
  body('finger_position')
    .optional()
    .isIn([
      'right_thumb', 'right_index', 'right_middle', 'right_ring', 'right_little',
      'left_thumb', 'left_index', 'left_middle', 'left_ring', 'left_little'
    ])
    .withMessage('Invalid finger position'),
  body('device_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Device ID must not exceed 100 characters')
];

const staffIdValidation = [
  param('staff_id')
    .notEmpty()
    .withMessage('Staff ID is required')
];

// Routes
router.post('/enroll', 
  enrollFingerprintValidation, 
  authorize('manage_biometric', 'enroll_biometric'), 
  BiometricController.enrollFingerprint
);

router.post('/verify', 
  verifyFingerprintValidation, 
  authorize('verify_biometric', 'mark_attendance'), 
  BiometricController.verifyFingerprint
);

router.get('/stats', 
  authorize('view_reports', 'manage_biometric'), 
  BiometricController.getBiometricStats
);

router.get('/:staff_id', 
  staffIdValidation, 
  requireOwnershipOrManager, 
  BiometricController.getBiometricData
);

router.put('/:staff_id', 
  staffIdValidation, 
  updateBiometricValidation, 
  authorize('manage_biometric'), 
  BiometricController.updateBiometricData
);

router.delete('/:staff_id', 
  staffIdValidation, 
  requireAdmin, 
  BiometricController.deleteBiometricData
);

module.exports = router;
