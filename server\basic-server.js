const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  
  console.log(`${req.method} ${path}`);

  // Health check
  if (path === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'OK', message: 'Server is running!' }));
    return;
  }

  // Mock login endpoint
  if (path === '/api/auth/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const { email, employee_id, password } = data;

        console.log('Login attempt:', { email, employee_id, password });

        // Mock user database
        const users = [
          {
            email: '<EMAIL>',
            employee_id: 'EMP001',
            password: 'admin123',
            staff_id: 'STF-ADMIN-001',
            first_name: 'System',
            last_name: 'Administrator',
            role: { name: 'Super Admin', is_admin: true, permissions: ['*'] }
          },
          {
            email: '<EMAIL>',
            employee_id: 'EMP002',
            password: 'password123',
            staff_id: 'STF-HR-001',
            first_name: 'Sarah',
            last_name: 'Johnson',
            role: { name: 'HR Manager', is_admin: false, permissions: ['manage_staff', 'view_reports'] }
          },
          {
            email: '<EMAIL>',
            employee_id: 'EMP003',
            password: 'password123',
            staff_id: 'STF-IT-001',
            first_name: 'Michael',
            last_name: 'Chen',
            role: { name: 'IT Manager', is_admin: false, permissions: ['manage_staff', 'view_reports'] }
          },
          {
            email: '<EMAIL>',
            employee_id: 'EMP004',
            password: 'password123',
            staff_id: 'STF-FIN-001',
            first_name: 'Emily',
            last_name: 'Davis',
            role: { name: 'Finance Team Lead', is_admin: false, permissions: ['view_reports'] }
          },
          {
            email: '<EMAIL>',
            employee_id: 'EMP005',
            password: 'password123',
            staff_id: 'STF-MKT-001',
            first_name: 'David',
            last_name: 'Wilson',
            role: { name: 'Senior Marketing Specialist', is_admin: false, permissions: ['view_own_data'] }
          }
        ];

        // Find user by email or employee_id
        const user = users.find(u =>
          (email && u.email.toLowerCase() === email.toLowerCase()) ||
          (employee_id && u.employee_id === employee_id)
        );

        if (user && user.password === password) {
          console.log('Login successful for:', user.first_name, user.last_name);
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            data: {
              staff: {
                staff_id: user.staff_id,
                first_name: user.first_name,
                last_name: user.last_name,
                email: user.email,
                employee_id: user.employee_id,
                role: user.role
              },
              tokens: {
                accessToken: `mock-token-${user.staff_id}`,
                refreshToken: `mock-refresh-${user.staff_id}`
              }
            }
          }));
        } else {
          console.log('Login failed for:', email || employee_id);
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: 'Invalid credentials'
          }));
        }
      } catch (error) {
        console.error('Login error:', error);
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Mock dashboard data
  if (path === '/api/reports/dashboard') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: {
        today: {
          total_present: 8,
          total_late: 2,
          total_checked_out: 5,
          still_in_office: 3,
          attendance_rate: '80.0'
        },
        staff: {
          total_staff: 10,
          biometric_enrolled: 5,
          enrollment_rate: '50.0'
        },
        leaves: {
          pending_approvals: 3,
          monthly_approved: 12
        }
      }
    }));
    return;
  }

  // Default 404
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Not found' }));
});

const PORT = 5000;
server.listen(PORT, () => {
  console.log(`🚀 Basic server running on http://localhost:${PORT}`);
  console.log(`📚 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Login endpoint: http://localhost:${PORT}/api/auth/login`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});
