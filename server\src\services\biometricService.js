const { <PERSON>io<PERSON><PERSON><PERSON>, Staff } = require('../models');
const logger = require('../utils/logger');

class BiometricService {
  constructor() {
    this.deviceConnections = new Map();
    this.isSimulationMode = process.env.BIOMETRIC_SIMULATION === 'true' || true;
  }

  // Initialize biometric device connection
  async initializeDevice(deviceId, deviceConfig = {}) {
    try {
      if (this.isSimulationMode) {
        // Simulate device connection
        this.deviceConnections.set(deviceId, {
          id: deviceId,
          status: 'connected',
          type: 'fingerprint_scanner',
          model: 'ESSL K40',
          firmware: '1.0.0',
          lastHeartbeat: new Date(),
          ...deviceConfig
        });

        logger.info(`Biometric device ${deviceId} initialized (simulation mode)`);
        return true;
      }

      // Real device initialization would go here
      // Example: Connect to device via TCP/IP or USB
      // const device = await this.connectToDevice(deviceConfig);
      
      return false;
    } catch (error) {
      logger.error(`Failed to initialize biometric device ${deviceId}:`, error);
      return false;
    }
  }

  // Simulate fingerprint capture
  async captureFingerprint(deviceId, staffId = null) {
    try {
      if (!this.deviceConnections.has(deviceId)) {
        throw new Error(`Device ${deviceId} not connected`);
      }

      if (this.isSimulationMode) {
        // Simulate fingerprint capture
        const quality = Math.floor(Math.random() * 30) + 70; // 70-100 quality
        const template = this.generateSimulatedTemplate(staffId);
        
        return {
          success: true,
          template,
          quality,
          captureTime: new Date(),
          deviceId
        };
      }

      // Real fingerprint capture would go here
      return { success: false, error: 'Real device not implemented' };
    } catch (error) {
      logger.error(`Fingerprint capture failed on device ${deviceId}:`, error);
      return { success: false, error: error.message };
    }
  }

  // Verify fingerprint against stored template
  async verifyFingerprint(deviceId, capturedTemplate, staffId) {
    try {
      if (!this.deviceConnections.has(deviceId)) {
        throw new Error(`Device ${deviceId} not connected`);
      }

      // Get stored biometric data
      const biometricData = await BiometricData.findOne({
        where: { staff_id: staffId, is_active: true }
      });

      if (!biometricData) {
        return {
          success: false,
          error: 'No biometric data found for staff',
          matchScore: 0
        };
      }

      if (this.isSimulationMode) {
        const storedTemplate = biometricData.getDecryptedTemplate();
        const matchScore = this.simulateTemplateMatching(capturedTemplate, storedTemplate);
        const isMatch = matchScore >= 80; // 80% threshold

        // Update verification statistics
        await biometricData.updateVerification(isMatch);

        return {
          success: isMatch,
          matchScore,
          verificationTime: new Date(),
          deviceId,
          staffId
        };
      }

      // Real verification would go here
      return { success: false, error: 'Real device not implemented' };
    } catch (error) {
      logger.error(`Fingerprint verification failed:`, error);
      return { success: false, error: error.message };
    }
  }

  // Enroll fingerprint for staff
  async enrollFingerprint(deviceId, staffId, fingerPosition = 'right_index') {
    try {
      if (!this.deviceConnections.has(deviceId)) {
        throw new Error(`Device ${deviceId} not connected`);
      }

      // Check if staff exists
      const staff = await Staff.findByPk(staffId);
      if (!staff) {
        throw new Error('Staff not found');
      }

      // Check if already enrolled
      const existingBiometric = await BiometricData.findOne({
        where: { staff_id: staffId }
      });

      if (existingBiometric) {
        throw new Error('Staff already has biometric data enrolled');
      }

      // Capture multiple samples for better template
      const samples = [];
      for (let i = 0; i < 3; i++) {
        const capture = await this.captureFingerprint(deviceId, staffId);
        if (!capture.success) {
          throw new Error(`Failed to capture sample ${i + 1}: ${capture.error}`);
        }
        samples.push(capture);
      }

      // Select best quality sample
      const bestSample = samples.reduce((best, current) => 
        current.quality > best.quality ? current : best
      );

      if (bestSample.quality < 70) {
        throw new Error('Fingerprint quality too low. Please try again.');
      }

      // Create biometric data
      const biometricData = await BiometricData.create({
        staff_id: staffId,
        fingerprint_template: bestSample.template,
        template_quality: bestSample.quality,
        finger_position: fingerPosition,
        device_id: deviceId,
        enrollment_date: new Date()
      });

      // Update staff enrollment status
      await staff.update({ is_biometric_enrolled: true });

      logger.info(`Fingerprint enrolled for staff ${staffId} on device ${deviceId}`);

      return {
        success: true,
        biometricId: biometricData.id,
        quality: bestSample.quality,
        enrollmentDate: biometricData.enrollment_date
      };

    } catch (error) {
      logger.error(`Fingerprint enrollment failed:`, error);
      return { success: false, error: error.message };
    }
  }

  // Get device status
  getDeviceStatus(deviceId) {
    if (this.deviceConnections.has(deviceId)) {
      return this.deviceConnections.get(deviceId);
    }
    return { status: 'disconnected' };
  }

  // Get all connected devices
  getAllDevices() {
    return Array.from(this.deviceConnections.values());
  }

  // Simulate template matching
  simulateTemplateMatching(template1, template2) {
    if (!template1 || !template2) return 0;
    
    // Simple simulation based on string similarity
    if (template1 === template2) {
      return Math.floor(Math.random() * 20) + 80; // 80-100% match
    }
    
    // Calculate similarity based on common characters
    const similarity = this.calculateStringSimilarity(template1, template2);
    return Math.floor(similarity * 100);
  }

  // Calculate string similarity
  calculateStringSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // Levenshtein distance calculation
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  // Generate simulated fingerprint template
  generateSimulatedTemplate(staffId = null) {
    // Generate a consistent template for the same staff ID
    const seed = staffId ? staffId.split('').reduce((a, b) => a + b.charCodeAt(0), 0) : Math.random();
    const template = [];
    
    for (let i = 0; i < 256; i++) {
      const value = Math.floor((Math.sin(seed + i) * 10000) % 256);
      template.push(Math.abs(value).toString(16).padStart(2, '0'));
    }
    
    return template.join('');
  }

  // Device heartbeat monitoring
  startHeartbeatMonitoring() {
    setInterval(() => {
      this.deviceConnections.forEach((device, deviceId) => {
        const timeSinceLastHeartbeat = Date.now() - device.lastHeartbeat.getTime();
        
        if (timeSinceLastHeartbeat > 60000) { // 1 minute timeout
          device.status = 'disconnected';
          logger.warn(`Device ${deviceId} heartbeat timeout`);
        } else {
          device.status = 'connected';
          device.lastHeartbeat = new Date();
        }
      });
    }, 30000); // Check every 30 seconds
  }

  // Cleanup disconnected devices
  cleanup() {
    this.deviceConnections.forEach((device, deviceId) => {
      if (device.status === 'disconnected') {
        this.deviceConnections.delete(deviceId);
        logger.info(`Removed disconnected device ${deviceId}`);
      }
    });
  }
}

// Export singleton instance
module.exports = new BiometricService();
