const jwt = require('jsonwebtoken');
const { redisClient } = require('../config/redis');

class JWTUtils {
  static generateTokens(payload) {
    const accessToken = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { 
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
        issuer: 'staff-attendance-system',
        audience: 'staff-attendance-client'
      }
    );

    const refreshToken = jwt.sign(
      { staff_id: payload.staff_id },
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      { 
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        issuer: 'staff-attendance-system',
        audience: 'staff-attendance-client'
      }
    );

    return { accessToken, refreshToken };
  }

  static verifyAccessToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET, {
        issuer: 'staff-attendance-system',
        audience: 'staff-attendance-client'
      });
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  static verifyRefreshToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET, {
        issuer: 'staff-attendance-system',
        audience: 'staff-attendance-client'
      });
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  static async blacklistToken(token) {
    try {
      const decoded = jwt.decode(token);
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redisClient.setEx(`blacklist:${token}`, ttl, 'true');
        }
      }
    } catch (error) {
      console.error('Error blacklisting token:', error);
    }
  }

  static async isTokenBlacklisted(token) {
    try {
      const result = await redisClient.get(`blacklist:${token}`);
      return result === 'true';
    } catch (error) {
      console.error('Error checking token blacklist:', error);
      return false;
    }
  }

  static async storeRefreshToken(staffId, refreshToken) {
    try {
      const decoded = jwt.decode(refreshToken);
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redisClient.setEx(`refresh:${staffId}`, ttl, refreshToken);
        }
      }
    } catch (error) {
      console.error('Error storing refresh token:', error);
    }
  }

  static async getStoredRefreshToken(staffId) {
    try {
      return await redisClient.get(`refresh:${staffId}`);
    } catch (error) {
      console.error('Error getting stored refresh token:', error);
      return null;
    }
  }

  static async removeRefreshToken(staffId) {
    try {
      await redisClient.del(`refresh:${staffId}`);
    } catch (error) {
      console.error('Error removing refresh token:', error);
    }
  }

  static extractTokenFromHeader(authHeader) {
    if (!authHeader) {
      throw new Error('No authorization header provided');
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new Error('Invalid authorization header format');
    }

    return parts[1];
  }

  static getTokenPayload(staff) {
    return {
      staff_id: staff.staff_id,
      employee_id: staff.employee_id,
      email: staff.email,
      role: {
        id: staff.role?.id,
        name: staff.role?.name,
        permissions: staff.role?.getPermissionsList() || []
      },
      department: {
        id: staff.department?.id,
        name: staff.department?.name,
        code: staff.department?.code
      },
      is_biometric_enrolled: staff.is_biometric_enrolled
    };
  }

  static async refreshAccessToken(refreshToken) {
    try {
      // Verify refresh token
      const decoded = this.verifyRefreshToken(refreshToken);
      
      // Check if refresh token is stored
      const storedToken = await this.getStoredRefreshToken(decoded.staff_id);
      if (!storedToken || storedToken !== refreshToken) {
        throw new Error('Invalid refresh token');
      }

      // Get staff data (you'll need to import Staff model)
      const { Staff } = require('../models');
      const staff = await Staff.findByPk(decoded.staff_id, {
        include: ['role', 'department']
      });

      if (!staff || staff.status !== 'active') {
        throw new Error('Staff not found or inactive');
      }

      // Generate new access token
      const payload = this.getTokenPayload(staff);
      const { accessToken } = this.generateTokens(payload);

      return { accessToken };
    } catch (error) {
      throw new Error('Failed to refresh token: ' + error.message);
    }
  }

  static decodeToken(token) {
    return jwt.decode(token);
  }

  static getTokenExpiration(token) {
    const decoded = this.decodeToken(token);
    return decoded ? new Date(decoded.exp * 1000) : null;
  }

  static isTokenExpired(token) {
    const expiration = this.getTokenExpiration(token);
    return expiration ? expiration < new Date() : true;
  }
}

module.exports = JWTUtils;
