const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const crypto = require('crypto');

const BiometricData = sequelize.define('BiometricData', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  staff_id: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  fingerprint_template: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Encrypted fingerprint template data'
  },
  template_quality: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 0,
      max: 100
    },
    comment: 'Quality score of the fingerprint template (0-100)'
  },
  finger_position: {
    type: DataTypes.ENUM(
      'right_thumb', 'right_index', 'right_middle', 'right_ring', 'right_little',
      'left_thumb', 'left_index', 'left_middle', 'left_ring', 'left_little'
    ),
    allowNull: false,
    defaultValue: 'right_index'
  },
  enrollment_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  last_verification: {
    type: DataTypes.DATE,
    allowNull: true
  },
  verification_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  failed_attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  device_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'ID of the device used for enrollment'
  },
  backup_templates: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional fingerprint templates for backup'
  },
  encryption_version: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: '1.0'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'biometric_data',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['staff_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['enrollment_date']
    }
  ],
  hooks: {
    beforeCreate: (biometricData) => {
      // Encrypt fingerprint template before saving
      if (biometricData.fingerprint_template) {
        biometricData.fingerprint_template = BiometricData.encryptTemplate(
          biometricData.fingerprint_template
        );
      }
    },
    beforeUpdate: (biometricData) => {
      // Encrypt fingerprint template if it's being updated
      if (biometricData.changed('fingerprint_template')) {
        biometricData.fingerprint_template = BiometricData.encryptTemplate(
          biometricData.fingerprint_template
        );
      }
    }
  }
});

// Static methods for encryption/decryption
BiometricData.encryptTemplate = function(template) {
  const algorithm = process.env.ENCRYPTION_ALGORITHM || 'aes-256-cbc';
  const key = process.env.ENCRYPTION_KEY;
  
  if (!key) {
    throw new Error('Encryption key not configured');
  }
  
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  
  let encrypted = cipher.update(template, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
};

BiometricData.decryptTemplate = function(encryptedTemplate) {
  const algorithm = process.env.ENCRYPTION_ALGORITHM || 'aes-256-cbc';
  const key = process.env.ENCRYPTION_KEY;
  
  if (!key) {
    throw new Error('Encryption key not configured');
  }
  
  const parts = encryptedTemplate.split(':');
  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  
  const decipher = crypto.createDecipher(algorithm, key);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

// Instance methods
BiometricData.prototype.getDecryptedTemplate = function() {
  return BiometricData.decryptTemplate(this.fingerprint_template);
};

BiometricData.prototype.updateVerification = function(success = true) {
  this.last_verification = new Date();
  
  if (success) {
    this.verification_count += 1;
    this.failed_attempts = 0; // Reset failed attempts on successful verification
  } else {
    this.failed_attempts += 1;
  }
  
  return this.save();
};

BiometricData.prototype.isTemplateExpired = function(expiryDays = 365) {
  const expiryDate = new Date(this.enrollment_date);
  expiryDate.setDate(expiryDate.getDate() + expiryDays);
  
  return new Date() > expiryDate;
};

BiometricData.prototype.shouldReenroll = function() {
  // Suggest re-enrollment if:
  // 1. Template is older than 1 year
  // 2. Failed attempts are high
  // 3. Template quality is low
  
  const isExpired = this.isTemplateExpired();
  const highFailureRate = this.failed_attempts > 10;
  const lowQuality = this.template_quality < 70;
  
  return isExpired || highFailureRate || lowQuality;
};

BiometricData.prototype.getSecurityInfo = function() {
  return {
    enrollment_date: this.enrollment_date,
    last_verification: this.last_verification,
    verification_count: this.verification_count,
    template_quality: this.template_quality,
    finger_position: this.finger_position,
    is_active: this.is_active,
    should_reenroll: this.shouldReenroll()
  };
};

module.exports = BiometricData;
