const { sequelize, Staff, Department, Role, BiometricData } = require('../src/models');
const bcrypt = require('bcryptjs');

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Sync database
    await sequelize.sync({ force: true });
    console.log('✅ Database synced');

    // Create Departments
    const departments = await Department.bulkCreate([
      {
        name: 'Human Resources',
        code: 'HR',
        description: 'Human Resources Department',
        location: 'Building A, Floor 2',
        phone: '******-0101',
        email: '<EMAIL>'
      },
      {
        name: 'Information Technology',
        code: 'IT',
        description: 'Information Technology Department',
        location: 'Building B, Floor 3',
        phone: '******-0102',
        email: '<EMAIL>'
      },
      {
        name: 'Finance',
        code: 'FIN',
        description: 'Finance Department',
        location: 'Building A, Floor 1',
        phone: '******-0103',
        email: '<EMAIL>'
      },
      {
        name: 'Marketing',
        code: 'MKT',
        description: 'Marketing Department',
        location: 'Building C, Floor 2',
        phone: '******-0104',
        email: '<EMAIL>'
      },
      {
        name: 'Operations',
        code: 'OPS',
        description: 'Operations Department',
        location: 'Building B, Floor 1',
        phone: '******-0105',
        email: '<EMAIL>'
      }
    ]);
    console.log('✅ Departments created');

    // Create Roles
    const roles = await Role.bulkCreate([
      {
        name: 'Super Admin',
        description: 'Full system access',
        permissions: ['*'],
        level: 10,
        is_admin: true,
        is_manager: true,
        can_approve_leave: true,
        can_manage_staff: true,
        can_view_reports: true,
        can_modify_attendance: true
      },
      {
        name: 'HR Manager',
        description: 'Human Resources Manager',
        permissions: ['manage_staff', 'view_reports', 'approve_leave'],
        level: 8,
        is_manager: true,
        can_approve_leave: true,
        can_manage_staff: true,
        can_view_reports: true,
        max_leave_days: 30
      },
      {
        name: 'Department Manager',
        description: 'Department Manager',
        permissions: ['view_reports', 'approve_leave'],
        level: 7,
        is_manager: true,
        can_approve_leave: true,
        can_view_reports: true,
        max_leave_days: 15
      },
      {
        name: 'Team Lead',
        description: 'Team Lead',
        permissions: ['view_team_reports'],
        level: 5,
        can_approve_leave: true,
        can_view_reports: true,
        max_leave_days: 7
      },
      {
        name: 'Senior Employee',
        description: 'Senior Employee',
        permissions: ['view_own_data'],
        level: 3
      },
      {
        name: 'Employee',
        description: 'Regular Employee',
        permissions: ['view_own_data'],
        level: 1
      }
    ]);
    console.log('✅ Roles created');

    // Create Staff
    const staff = await Staff.bulkCreate([
      {
        staff_id: 'STF-ADMIN-001',
        employee_id: 'EMP001',
        first_name: 'System',
        last_name: 'Administrator',
        email: '<EMAIL>',
        password: 'admin123',
        phone: '******-1001',
        department_id: departments[1].id, // IT
        role_id: roles[0].id, // Super Admin
        position: 'System Administrator',
        hire_date: '2023-01-01',
        salary: 80000,
        shift_start: '09:00:00',
        shift_end: '17:00:00',
        address: '123 Admin Street, Tech City',
        emergency_contact_name: 'Emergency Contact',
        emergency_contact_phone: '******-9999'
      },
      {
        staff_id: 'STF-HR-001',
        employee_id: 'EMP002',
        first_name: 'Sarah',
        last_name: 'Johnson',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1002',
        department_id: departments[0].id, // HR
        role_id: roles[1].id, // HR Manager
        position: 'HR Manager',
        hire_date: '2023-02-01',
        salary: 75000,
        shift_start: '08:30:00',
        shift_end: '16:30:00',
        address: '456 HR Avenue, Business District'
      },
      {
        staff_id: 'STF-IT-001',
        employee_id: 'EMP003',
        first_name: 'Michael',
        last_name: 'Chen',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1003',
        department_id: departments[1].id, // IT
        role_id: roles[2].id, // Department Manager
        position: 'IT Manager',
        hire_date: '2023-01-15',
        salary: 85000,
        shift_start: '09:00:00',
        shift_end: '17:00:00',
        address: '789 Tech Boulevard, Innovation Hub'
      },
      {
        staff_id: 'STF-FIN-001',
        employee_id: 'EMP004',
        first_name: 'Emily',
        last_name: 'Davis',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1004',
        department_id: departments[2].id, // Finance
        role_id: roles[3].id, // Team Lead
        position: 'Finance Team Lead',
        hire_date: '2023-03-01',
        salary: 65000,
        shift_start: '08:00:00',
        shift_end: '16:00:00',
        address: '321 Finance Street, Money District'
      },
      {
        staff_id: 'STF-MKT-001',
        employee_id: 'EMP005',
        first_name: 'David',
        last_name: 'Wilson',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1005',
        department_id: departments[3].id, // Marketing
        role_id: roles[4].id, // Senior Employee
        position: 'Senior Marketing Specialist',
        hire_date: '2023-04-01',
        salary: 60000,
        shift_start: '09:30:00',
        shift_end: '17:30:00',
        address: '654 Creative Lane, Art Quarter'
      },
      {
        staff_id: 'STF-OPS-001',
        employee_id: 'EMP006',
        first_name: 'Lisa',
        last_name: 'Brown',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1006',
        department_id: departments[4].id, // Operations
        role_id: roles[5].id, // Employee
        position: 'Operations Coordinator',
        hire_date: '2023-05-01',
        salary: 50000,
        shift_start: '08:00:00',
        shift_end: '16:00:00',
        address: '987 Operations Drive, Industrial Zone'
      },
      {
        staff_id: 'STF-IT-002',
        employee_id: 'EMP007',
        first_name: 'James',
        last_name: 'Rodriguez',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1007',
        department_id: departments[1].id, // IT
        role_id: roles[4].id, // Senior Employee
        position: 'Senior Developer',
        hire_date: '2023-02-15',
        salary: 70000,
        shift_start: '10:00:00',
        shift_end: '18:00:00',
        address: '147 Code Street, Developer Heights'
      },
      {
        staff_id: 'STF-HR-002',
        employee_id: 'EMP008',
        first_name: 'Amanda',
        last_name: 'Taylor',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1008',
        department_id: departments[0].id, // HR
        role_id: roles[5].id, // Employee
        position: 'HR Coordinator',
        hire_date: '2023-06-01',
        salary: 45000,
        shift_start: '08:30:00',
        shift_end: '16:30:00',
        address: '258 People Street, Community Center'
      }
    ]);
    console.log('✅ Staff created');

    // Create sample biometric data for some staff
    const biometricService = require('../src/services/biometricService');
    
    // Initialize a simulated device
    await biometricService.initializeDevice('DEVICE-001', {
      location: 'Main Entrance',
      model: 'ESSL K40'
    });

    // Enroll fingerprints for first 5 staff members
    for (let i = 0; i < 5; i++) {
      const staffMember = staff[i];
      const template = biometricService.generateSimulatedTemplate(staffMember.staff_id);
      
      await BiometricData.create({
        staff_id: staffMember.staff_id,
        fingerprint_template: template,
        template_quality: Math.floor(Math.random() * 20) + 80, // 80-100
        finger_position: 'right_index',
        device_id: 'DEVICE-001',
        enrollment_date: new Date()
      });

      // Update staff enrollment status
      await staffMember.update({ is_biometric_enrolled: true });
    }
    console.log('✅ Biometric data created for 5 staff members');

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📋 Sample Login Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('HR Manager: <EMAIL> / password123');
    console.log('IT Manager: <EMAIL> / password123');
    console.log('\n🔐 All other staff: password123');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = seedDatabase;
