const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Leave = sequelize.define('Leave', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  leave_id: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  staff_id: {
    type: DataTypes.STRING(20),
    allowNull: false,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  leave_type: {
    type: DataTypes.ENUM(
      'annual', 'sick', 'maternity', 'paternity', 'emergency', 
      'bereavement', 'study', 'unpaid', 'compensatory', 'other'
    ),
    allowNull: false
  },
  start_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  end_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  total_days: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending'
  },
  applied_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  approved_by: {
    type: DataTypes.STRING(20),
    allowNull: true,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  approved_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_half_day: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  half_day_period: {
    type: DataTypes.ENUM('morning', 'afternoon'),
    allowNull: true
  },
  emergency_contact: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  supporting_documents: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of document URLs/paths'
  },
  substitute_staff_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    references: {
      model: 'staff',
      key: 'staff_id'
    }
  },
  handover_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_paid: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'leaves',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['staff_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['leave_type']
    },
    {
      fields: ['start_date', 'end_date']
    },
    {
      fields: ['staff_id', 'status']
    }
  ],
  hooks: {
    beforeCreate: (leave) => {
      // Generate unique leave ID
      const timestamp = Date.now().toString(36);
      const random = Math.random().toString(36).substr(2, 5);
      leave.leave_id = `LV-${timestamp}-${random}`.toUpperCase();
      
      // Calculate total days
      const startDate = new Date(leave.start_date);
      const endDate = new Date(leave.end_date);
      const timeDiff = endDate.getTime() - startDate.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
      
      leave.total_days = leave.is_half_day ? 0.5 : daysDiff;
    },
    beforeUpdate: (leave) => {
      // Recalculate total days if dates changed
      if (leave.changed('start_date') || leave.changed('end_date') || leave.changed('is_half_day')) {
        const startDate = new Date(leave.start_date);
        const endDate = new Date(leave.end_date);
        const timeDiff = endDate.getTime() - startDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
        
        leave.total_days = leave.is_half_day ? 0.5 : daysDiff;
      }
      
      // Set approved_date when status changes to approved
      if (leave.changed('status') && leave.status === 'approved') {
        leave.approved_date = new Date();
      }
    }
  },
  validate: {
    endDateAfterStartDate() {
      if (this.end_date < this.start_date) {
        throw new Error('End date must be after start date');
      }
    },
    halfDayValidation() {
      if (this.is_half_day && this.start_date !== this.end_date) {
        throw new Error('Half day leave must be for a single day');
      }
    },
    futureDateValidation() {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const startDate = new Date(this.start_date);
      
      // Allow same day applications for emergency leaves
      if (this.leave_type !== 'emergency' && startDate < today) {
        throw new Error('Leave start date cannot be in the past');
      }
    }
  }
});

// Instance methods
Leave.prototype.getDuration = function() {
  if (this.is_half_day) {
    return `Half Day (${this.half_day_period})`;
  }
  
  const days = this.total_days;
  return days === 1 ? '1 day' : `${days} days`;
};

Leave.prototype.isOverlapping = async function(staffId, excludeId = null) {
  const whereClause = {
    staff_id: staffId,
    status: ['pending', 'approved'],
    [sequelize.Op.or]: [
      {
        start_date: {
          [sequelize.Op.between]: [this.start_date, this.end_date]
        }
      },
      {
        end_date: {
          [sequelize.Op.between]: [this.start_date, this.end_date]
        }
      },
      {
        [sequelize.Op.and]: [
          { start_date: { [sequelize.Op.lte]: this.start_date } },
          { end_date: { [sequelize.Op.gte]: this.end_date } }
        ]
      }
    ]
  };
  
  if (excludeId) {
    whereClause.id = { [sequelize.Op.ne]: excludeId };
  }
  
  const overlappingLeaves = await Leave.findAll({ where: whereClause });
  return overlappingLeaves.length > 0;
};

Leave.prototype.canBeModified = function() {
  return this.status === 'pending';
};

Leave.prototype.canBeCancelled = function() {
  return ['pending', 'approved'].includes(this.status) && 
         new Date(this.start_date) > new Date();
};

module.exports = Leave;
