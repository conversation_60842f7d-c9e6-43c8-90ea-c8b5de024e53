const { Leave, Staff, AuditLog } = require('../models');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

class LeaveController {
  // Apply for leave
  static async applyLeave(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const staffId = req.user.staff_id;
      const leaveData = {
        ...req.body,
        staff_id: staffId,
        applied_date: new Date()
      };

      // Check for overlapping leaves
      const leave = new Leave(leaveData);
      const hasOverlap = await leave.isOverlapping(staffId);
      
      if (hasOverlap) {
        return res.status(400).json({
          success: false,
          error: 'Leave dates overlap with existing leave application'
        });
      }

      const createdLeave = await Leave.create(leaveData);

      // Load associations
      await createdLeave.reload({
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id']
        }]
      });

      // Log leave application
      await AuditLog.logLeaveAction(
        staffId,
        'apply_leave',
        createdLeave.toJSON(),
        null,
        req.ip
      );

      logger.logLeave('apply', staffId, createdLeave.leave_id, {
        type: createdLeave.leave_type,
        duration: createdLeave.getDuration()
      });

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('leave_applied', {
        leave: createdLeave.toJSON(),
        staff: createdLeave.staff
      });

      res.status(201).json({
        success: true,
        message: 'Leave application submitted successfully',
        data: createdLeave
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get all leaves with filtering
  static async getAllLeaves(req, res) {
    try {
      const {
        staff_id,
        status,
        leave_type,
        start_date,
        end_date,
        page = 1,
        limit = 10,
        sortBy = 'applied_date',
        sortOrder = 'DESC'
      } = req.query;

      const whereClause = {};
      const offset = (page - 1) * limit;

      // Add filters
      if (staff_id) {
        whereClause.staff_id = staff_id;
      }

      if (status) {
        whereClause.status = status;
      }

      if (leave_type) {
        whereClause.leave_type = leave_type;
      }

      if (start_date && end_date) {
        whereClause[Op.or] = [
          {
            start_date: {
              [Op.between]: [start_date, end_date]
            }
          },
          {
            end_date: {
              [Op.between]: [start_date, end_date]
            }
          }
        ];
      }

      const { count, rows: leaves } = await Leave.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Staff,
            as: 'staff',
            attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
            include: ['department']
          },
          {
            model: Staff,
            as: 'approver',
            attributes: ['staff_id', 'first_name', 'last_name'],
            required: false
          }
        ],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      res.json({
        success: true,
        data: {
          leaves,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get leave by ID
  static async getLeaveById(req, res) {
    try {
      const { id } = req.params;

      const leave = await Leave.findByPk(id, {
        include: [
          {
            model: Staff,
            as: 'staff',
            attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
            include: ['department']
          },
          {
            model: Staff,
            as: 'approver',
            attributes: ['staff_id', 'first_name', 'last_name'],
            required: false
          }
        ]
      });

      if (!leave) {
        return res.status(404).json({
          success: false,
          error: 'Leave not found'
        });
      }

      res.json({
        success: true,
        data: leave
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Update leave (only for pending leaves)
  static async updateLeave(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const leave = await Leave.findByPk(id);

      if (!leave) {
        return res.status(404).json({
          success: false,
          error: 'Leave not found'
        });
      }

      if (!leave.canBeModified()) {
        return res.status(400).json({
          success: false,
          error: 'Leave cannot be modified in current status'
        });
      }

      const oldValues = leave.toJSON();
      await leave.update(req.body);

      // Log update
      await AuditLog.logLeaveAction(
        req.user.staff_id,
        'update_leave',
        leave.toJSON(),
        oldValues,
        req.ip
      );

      logger.logLeave('update', req.user.staff_id, leave.leave_id, {
        changes: req.body
      });

      res.json({
        success: true,
        message: 'Leave updated successfully',
        data: leave
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Approve or reject leave
  static async approveLeave(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const { status, rejection_reason } = req.body;
      const approverId = req.user.staff_id;

      const leave = await Leave.findByPk(id, {
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id']
        }]
      });

      if (!leave) {
        return res.status(404).json({
          success: false,
          error: 'Leave not found'
        });
      }

      if (leave.status !== 'pending') {
        return res.status(400).json({
          success: false,
          error: 'Leave has already been processed'
        });
      }

      // Check if approver has permission to approve this leave duration
      const canApprove = req.user.role?.canApproveLeave(leave.total_days);
      if (!canApprove) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permission to approve this leave duration'
        });
      }

      const oldValues = leave.toJSON();
      const updateData = {
        status,
        approved_by: approverId,
        approved_date: new Date()
      };

      if (status === 'rejected' && rejection_reason) {
        updateData.rejection_reason = rejection_reason;
      }

      await leave.update(updateData);

      // Log approval/rejection
      await AuditLog.logLeaveAction(
        approverId,
        status === 'approved' ? 'approve_leave' : 'reject_leave',
        leave.toJSON(),
        oldValues,
        req.ip
      );

      logger.logLeave(status, approverId, leave.leave_id, {
        staff_id: leave.staff_id,
        reason: rejection_reason
      });

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('leave_processed', {
        leave: leave.toJSON(),
        status,
        processed_by: approverId
      });

      res.json({
        success: true,
        message: `Leave ${status} successfully`,
        data: leave
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Cancel leave
  static async cancelLeave(req, res) {
    try {
      const { id } = req.params;
      const leave = await Leave.findByPk(id);

      if (!leave) {
        return res.status(404).json({
          success: false,
          error: 'Leave not found'
        });
      }

      if (!leave.canBeCancelled()) {
        return res.status(400).json({
          success: false,
          error: 'Leave cannot be cancelled'
        });
      }

      const oldValues = leave.toJSON();
      await leave.update({ status: 'cancelled' });

      // Log cancellation
      await AuditLog.logLeaveAction(
        req.user.staff_id,
        'cancel_leave',
        leave.toJSON(),
        oldValues,
        req.ip
      );

      logger.logLeave('cancel', req.user.staff_id, leave.leave_id, {});

      res.json({
        success: true,
        message: 'Leave cancelled successfully'
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get pending leaves for approval
  static async getPendingLeaves(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;
      const offset = (page - 1) * limit;

      const { count, rows: leaves } = await Leave.findAndCountAll({
        where: { status: 'pending' },
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
          include: ['department']
        }],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['applied_date', 'ASC']]
      });

      res.json({
        success: true,
        data: {
          leaves,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get leave statistics
  static async getLeaveStats(req, res) {
    try {
      const { staff_id, start_date, end_date } = req.query;
      const whereClause = {};

      if (staff_id) {
        whereClause.staff_id = staff_id;
      }

      if (start_date && end_date) {
        whereClause.start_date = {
          [Op.between]: [start_date, end_date]
        };
      }

      // Status-wise count
      const statusStats = await Leave.findAll({
        attributes: [
          'status',
          [Leave.sequelize.fn('COUNT', '*'), 'count'],
          [Leave.sequelize.fn('SUM', Leave.sequelize.col('total_days')), 'total_days']
        ],
        where: whereClause,
        group: ['status']
      });

      // Type-wise count
      const typeStats = await Leave.findAll({
        attributes: [
          'leave_type',
          [Leave.sequelize.fn('COUNT', '*'), 'count'],
          [Leave.sequelize.fn('SUM', Leave.sequelize.col('total_days')), 'total_days']
        ],
        where: { ...whereClause, status: 'approved' },
        group: ['leave_type']
      });

      res.json({
        success: true,
        data: {
          by_status: statusStats,
          by_type: typeStats
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = LeaveController;
