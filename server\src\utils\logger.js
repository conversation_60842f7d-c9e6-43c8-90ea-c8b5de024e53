const winston = require('winston');
const path = require('path');

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'staff-attendance-api' },
  transports: [
    // Write all logs with level 'error' and below to error.log
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Write all logs with level 'info' and below to combined.log
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Write all logs with level 'warn' and below to app.log
    new winston.transports.File({
      filename: path.join(logsDir, 'app.log'),
      level: 'warn',
      maxsize: 5242880, // 5MB
      maxFiles: 3
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// Create a stream object for Morgan HTTP request logging
logger.stream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

// Helper methods for structured logging
logger.logRequest = (req, res, responseTime) => {
  logger.info('HTTP Request', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userId: req.user?.staff_id || 'anonymous'
  });
};

logger.logError = (error, req = null) => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    name: error.name
  };
  
  if (req) {
    errorInfo.request = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.staff_id || 'anonymous'
    };
  }
  
  logger.error('Application Error', errorInfo);
};

logger.logSecurity = (event, details, req = null) => {
  const securityInfo = {
    event,
    details,
    timestamp: new Date().toISOString()
  };
  
  if (req) {
    securityInfo.request = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.staff_id || 'anonymous'
    };
  }
  
  logger.warn('Security Event', securityInfo);
};

logger.logAttendance = (action, staffId, details) => {
  logger.info('Attendance Event', {
    action,
    staffId,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.logBiometric = (action, staffId, deviceId, success) => {
  logger.info('Biometric Event', {
    action,
    staffId,
    deviceId,
    success,
    timestamp: new Date().toISOString()
  });
};

logger.logLeave = (action, staffId, leaveId, details) => {
  logger.info('Leave Event', {
    action,
    staffId,
    leaveId,
    details,
    timestamp: new Date().toISOString()
  });
};

module.exports = logger;
