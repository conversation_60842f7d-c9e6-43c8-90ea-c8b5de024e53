import React, { useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { 
  HomeIcon, 
  UsersIcon, 
  ClockIcon, 
  CalendarDaysIcon,
  ChartBarIcon,
  FingerPrintIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { useAuthStore } from '../../stores/authStore'

const DashboardLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  const { user, logout, hasPermission, isAdmin, isManager } = useAuthStore()

  const navigation = [
    { 
      name: 'Dashboard', 
      href: '/dashboard', 
      icon: HomeIcon,
      permissions: []
    },
    { 
      name: 'Staff Management', 
      href: '/staff', 
      icon: UsersIcon,
      permissions: ['view_staff', 'manage_staff']
    },
    { 
      name: 'Attendance', 
      href: '/attendance', 
      icon: ClockIcon,
      permissions: ['view_attendance', 'manage_attendance']
    },
    { 
      name: 'Leave Management', 
      href: '/leave', 
      icon: CalendarDaysIcon,
      permissions: ['view_leave', 'apply_leave']
    },
    { 
      name: 'Biometric', 
      href: '/biometric', 
      icon: FingerPrintIcon,
      permissions: ['manage_biometric', 'view_biometric']
    },
    { 
      name: 'Reports', 
      href: '/reports', 
      icon: ChartBarIcon,
      permissions: ['view_reports']
    }
  ]

  const userNavigation = [
    { name: 'Profile', href: '/profile', icon: UserCircleIcon },
    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, adminOnly: true }
  ]

  const handleLogout = async () => {
    await logout()
    navigate('/login')
  }

  const isCurrentPath = (path) => {
    return location.pathname === path
  }

  const canAccessRoute = (permissions) => {
    if (permissions.length === 0) return true
    return permissions.some(permission => hasPermission(permission))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>
          <SidebarContent 
            navigation={navigation}
            userNavigation={userNavigation}
            user={user}
            isCurrentPath={isCurrentPath}
            canAccessRoute={canAccessRoute}
            isAdmin={isAdmin}
            handleLogout={handleLogout}
          />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <SidebarContent 
          navigation={navigation}
          userNavigation={userNavigation}
          user={user}
          isCurrentPath={isCurrentPath}
          canAccessRoute={canAccessRoute}
          isAdmin={isAdmin}
          handleLogout={handleLogout}
        />
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top bar */}
        <div className="sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50">
          <button
            className="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

const SidebarContent = ({ 
  navigation, 
  userNavigation, 
  user, 
  isCurrentPath, 
  canAccessRoute, 
  isAdmin, 
  handleLogout 
}) => {
  return (
    <div className="flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <FingerPrintIcon className="w-5 h-5 text-white" />
          </div>
          <span className="ml-2 text-lg font-semibold text-gray-900">
            Staff Attendance
          </span>
        </div>

        {/* Navigation */}
        <nav className="mt-5 flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            if (!canAccessRoute(item.permissions)) return null
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`nav-item ${isCurrentPath(item.href) ? 'nav-item-active' : ''}`}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            )
          })}
        </nav>
      </div>

      {/* User section */}
      <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
        <div className="flex-shrink-0 w-full group block">
          <div className="flex items-center">
            <div>
              <div className="w-9 h-9 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-700">
                  {user?.first_name?.[0]}{user?.last_name?.[0]}
                </span>
              </div>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-700">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-xs text-gray-500">
                {user?.role?.name}
              </p>
            </div>
          </div>
          
          <div className="mt-3 space-y-1">
            {userNavigation.map((item) => {
              if (item.adminOnly && !isAdmin()) return null
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className="nav-item text-xs"
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.name}
                </Link>
              )
            })}
            
            <button
              onClick={handleLogout}
              className="nav-item text-xs w-full text-left"
            >
              <ArrowRightOnRectangleIcon className="mr-2 h-4 w-4" />
              Sign out
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardLayout
