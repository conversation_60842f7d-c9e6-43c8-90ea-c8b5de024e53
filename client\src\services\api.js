import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      try {
        const { state } = JSON.parse(authData)
        const token = state?.tokens?.accessToken
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.error('Error parsing auth data:', error)
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const authData = localStorage.getItem('auth-storage')
        if (authData) {
          const { state } = JSON.parse(authData)
          const refreshToken = state?.tokens?.refreshToken

          if (refreshToken) {
            const response = await axios.post('/api/auth/refresh-token', {
              refreshToken
            })

            const newAccessToken = response.data.data.accessToken
            
            // Update stored tokens
            const updatedState = {
              ...state,
              tokens: {
                ...state.tokens,
                accessToken: newAccessToken
              }
            }
            localStorage.setItem('auth-storage', JSON.stringify({ state: updatedState }))

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
            return api(originalRequest)
          }
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError)
        // Clear auth data and redirect to login
        localStorage.removeItem('auth-storage')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.')
    } else if (error.response?.status === 403) {
      toast.error('Access denied. Insufficient permissions.')
    } else if (error.response?.status === 404) {
      toast.error('Resource not found.')
    }

    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  refreshToken: (refreshToken) => api.post('/auth/refresh-token', { refreshToken }),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (data) => api.put('/auth/profile', data),
  changePassword: (data) => api.post('/auth/change-password', data),
  verifyToken: () => api.get('/auth/verify-token')
}

// Staff API
export const staffAPI = {
  getAll: (params) => api.get('/staff', { params }),
  getById: (id) => api.get(`/staff/${id}`),
  create: (data) => api.post('/staff', data),
  update: (id, data) => api.put(`/staff/${id}`, data),
  delete: (id) => api.delete(`/staff/${id}`),
  getStats: () => api.get('/staff/stats')
}

// Attendance API
export const attendanceAPI = {
  checkIn: (data) => api.post('/attendance/checkin', data),
  checkOut: (data) => api.post('/attendance/checkout', data),
  getAll: (params) => api.get('/attendance', { params }),
  getToday: () => api.get('/attendance/today'),
  getStats: (params) => api.get('/attendance/stats', { params })
}

// Leave API
export const leaveAPI = {
  getAll: (params) => api.get('/leave', { params }),
  getById: (id) => api.get(`/leave/${id}`),
  apply: (data) => api.post('/leave', data),
  update: (id, data) => api.put(`/leave/${id}`, data),
  approve: (id, data) => api.put(`/leave/${id}/approve`, data),
  cancel: (id) => api.delete(`/leave/${id}`),
  getPending: (params) => api.get('/leave/pending', { params }),
  getStats: (params) => api.get('/leave/stats', { params })
}

// Biometric API
export const biometricAPI = {
  enroll: (data) => api.post('/biometric/enroll', data),
  verify: (data) => api.post('/biometric/verify', data),
  getByStaffId: (staffId) => api.get(`/biometric/${staffId}`),
  update: (staffId, data) => api.put(`/biometric/${staffId}`, data),
  delete: (staffId) => api.delete(`/biometric/${staffId}`),
  getStats: () => api.get('/biometric/stats')
}

// Reports API
export const reportsAPI = {
  getAttendance: (params) => api.get('/reports/attendance', { params }),
  getLeave: (params) => api.get('/reports/leave', { params }),
  getStaff: (params) => api.get('/reports/staff', { params }),
  getDashboard: () => api.get('/reports/dashboard'),
  exportAttendance: (params) => api.get('/reports/export/attendance', { params }),
  exportLeave: (params) => api.get('/reports/export/leave', { params })
}

// Departments API (if needed)
export const departmentAPI = {
  getAll: () => api.get('/departments'),
  getById: (id) => api.get(`/departments/${id}`),
  create: (data) => api.post('/departments', data),
  update: (id, data) => api.put(`/departments/${id}`, data),
  delete: (id) => api.delete(`/departments/${id}`)
}

// Roles API (if needed)
export const roleAPI = {
  getAll: () => api.get('/roles'),
  getById: (id) => api.get(`/roles/${id}`),
  create: (data) => api.post('/roles', data),
  update: (id, data) => api.put(`/roles/${id}`, data),
  delete: (id) => api.delete(`/roles/${id}`)
}

export default api
