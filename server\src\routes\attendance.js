const express = require('express');
const { body, query } = require('express-validator');
const AttendanceController = require('../controllers/attendanceController');
const { authenticate, authorize, requireOwnershipOrManager } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation rules
const checkInValidation = [
  body('staff_id')
    .notEmpty()
    .withMessage('Staff ID is required'),
  body('verification_method')
    .optional()
    .isIn(['fingerprint', 'manual', 'card', 'face'])
    .withMessage('Invalid verification method'),
  body('device_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Device ID must not exceed 100 characters'),
  body('location')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Location must not exceed 255 characters')
];

const checkOutValidation = [
  body('staff_id')
    .notEmpty()
    .withMessage('Staff ID is required'),
  body('verification_method')
    .optional()
    .isIn(['fingerprint', 'manual', 'card', 'face'])
    .withMessage('Invalid verification method'),
  body('device_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Device ID must not exceed 100 characters')
];

const attendanceQueryValidation = [
  query('staff_id')
    .optional()
    .notEmpty()
    .withMessage('Staff ID cannot be empty'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
  query('status')
    .optional()
    .isIn(['present', 'absent', 'late', 'half_day', 'on_leave'])
    .withMessage('Invalid status'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sortBy')
    .optional()
    .isIn(['date', 'time_in', 'time_out', 'total_hours', 'status'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

const statsQueryValidation = [
  query('staff_id')
    .optional()
    .notEmpty()
    .withMessage('Staff ID cannot be empty'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date')
];

// Routes
router.post('/checkin', 
  checkInValidation, 
  authorize('mark_attendance', 'manage_attendance'), 
  AttendanceController.checkIn
);

router.post('/checkout', 
  checkOutValidation, 
  authorize('mark_attendance', 'manage_attendance'), 
  AttendanceController.checkOut
);

router.get('/', 
  attendanceQueryValidation, 
  authorize('view_attendance', 'manage_attendance'), 
  AttendanceController.getAttendance
);

router.get('/today', 
  authorize('view_attendance', 'manage_attendance'), 
  AttendanceController.getTodayAttendance
);

router.get('/stats', 
  statsQueryValidation, 
  authorize('view_reports', 'manage_attendance'), 
  AttendanceController.getAttendanceStats
);

module.exports = router;
