@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100;
  }
  
  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }

  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .input-error {
    @apply border-danger-500 focus-visible:ring-danger-500;
  }

  /* Card styles */
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-600;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply bg-primary-100 text-primary-800;
  }
  
  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }

  /* Table styles */
  .table {
    @apply w-full caption-bottom text-sm;
  }
  
  .table-header {
    @apply border-b;
  }
  
  .table-body {
    @apply [&_tr:last-child]:border-0;
  }
  
  .table-row {
    @apply border-b transition-colors hover:bg-gray-50 data-[state=selected]:bg-gray-100;
  }
  
  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0;
  }
  
  .table-cell {
    @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Sidebar styles */
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-200 ease-in-out;
  }
  
  .sidebar-open {
    @apply translate-x-0;
  }
  
  .sidebar-closed {
    @apply -translate-x-full;
  }

  /* Navigation styles */
  .nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors;
  }
  
  .nav-item-active {
    @apply bg-primary-100 text-primary-900 hover:bg-primary-200;
  }

  /* Form styles */
  .form-group {
    @apply space-y-2;
  }
  
  .form-label {
    @apply text-sm font-medium text-gray-700;
  }
  
  .form-error {
    @apply text-sm text-danger-600;
  }
  
  .form-help {
    @apply text-sm text-gray-500;
  }

  /* Status indicators */
  .status-dot {
    @apply inline-block w-2 h-2 rounded-full;
  }
  
  .status-online {
    @apply bg-success-500;
  }
  
  .status-offline {
    @apply bg-gray-400;
  }
  
  .status-busy {
    @apply bg-warning-500;
  }
  
  .status-away {
    @apply bg-danger-500;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}
