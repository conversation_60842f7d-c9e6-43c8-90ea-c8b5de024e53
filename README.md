# Staff Attendance Management System

A comprehensive staff attendance management system with biometric fingerprint integration, featuring real-time monitoring and automated data synchronization.

## Features

- **Biometric Authentication**: Fingerprint-based staff identification
- **Real-time Monitoring**: Live attendance tracking with WebSocket updates
- **Admin Dashboard**: Comprehensive management interface
- **Leave Management**: Automated leave application and approval system
- **Reporting & Analytics**: Detailed attendance reports and insights
- **Security**: Encrypted biometric data with JWT authentication

## Architecture

### Core Components
1. **Hardware Layer**: Biometric fingerprint scanners
2. **Data Collection**: Fingerprint registration and attendance logging
3. **Database Layer**: Staff, attendance, and leave management data
4. **Application Layer**: Admin portal and staff self-service
5. **Communication Layer**: Real-time updates and secure transmission

### Technology Stack
- **Backend**: Node.js with Express
- **Frontend**: React with Vite
- **Database**: PostgreSQL with Redis caching
- **Authentication**: JWT with biometric integration
- **Real-time**: WebSocket connections
- **Security**: AES-256 encryption

## Project Structure

```
staff-attendance-system/
├── server/                 # Backend API
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── models/         # Database models
│   │   ├── middleware/     # Authentication & validation
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Helper functions
│   ├── config/             # Configuration files
│   └── tests/              # Backend tests
├── client/                 # Frontend React app
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── hooks/          # Custom hooks
│   │   └── utils/          # Helper functions
│   └── public/             # Static assets
├── docs/                   # Documentation
└── scripts/                # Deployment scripts
```

## Quick Start

1. **Install Dependencies**
   ```bash
   npm run install:all
   ```

2. **Setup Environment**
   ```bash
   cp server/.env.example server/.env
   # Configure your database and other settings
   ```

3. **Start Development**
   ```bash
   npm run dev
   ```

4. **Access the Application**
   - Admin Dashboard: http://localhost:3000
   - API Documentation: http://localhost:5000/api/docs

## Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- Redis (v6 or higher)
- Biometric fingerprint scanner (ESSL or compatible)

### Environment Configuration
Create `.env` file in the server directory with:
```env
NODE_ENV=development
PORT=5000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=staff_attendance
DB_USER=your_username
DB_PASSWORD=your_password
REDIS_URL=redis://localhost:6379
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key
```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - Staff login
- `POST /api/auth/register-fingerprint` - Register biometric data
- `POST /api/auth/verify-fingerprint` - Verify attendance

### Staff Management
- `GET /api/staff` - List all staff
- `POST /api/staff` - Create new staff member
- `PUT /api/staff/:id` - Update staff information
- `DELETE /api/staff/:id` - Remove staff member

### Attendance Tracking
- `POST /api/attendance/checkin` - Record check-in
- `POST /api/attendance/checkout` - Record check-out
- `GET /api/attendance/today` - Today's attendance
- `GET /api/attendance/report` - Generate reports

### Leave Management
- `POST /api/leave/apply` - Apply for leave
- `GET /api/leave/pending` - Pending approvals
- `PUT /api/leave/:id/approve` - Approve/reject leave

## Security Features

- **Data Encryption**: AES-256 encryption for biometric templates
- **Secure Storage**: Fingerprints stored as encrypted templates, never as images
- **Access Control**: Role-based permissions (admin, manager, staff)
- **Audit Trail**: Complete logging of all system activities
- **HTTPS**: Secure data transmission
- **JWT Authentication**: Stateless authentication with refresh tokens

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.
