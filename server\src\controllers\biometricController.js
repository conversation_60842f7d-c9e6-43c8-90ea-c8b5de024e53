const { BiometricData, Staff, AuditLog } = require('../models');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class BiometricController {
  // Enroll fingerprint
  static async enrollFingerprint(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { 
        staff_id, 
        fingerprint_template, 
        template_quality, 
        finger_position = 'right_index',
        device_id 
      } = req.body;

      // Check if staff exists
      const staff = await Staff.findByPk(staff_id);
      if (!staff) {
        return res.status(404).json({
          success: false,
          error: 'Staff not found'
        });
      }

      // Check if biometric data already exists
      const existingBiometric = await BiometricData.findOne({
        where: { staff_id }
      });

      if (existingBiometric) {
        return res.status(400).json({
          success: false,
          error: 'Biometric data already enrolled for this staff member'
        });
      }

      // Validate template quality
      if (template_quality < 70) {
        return res.status(400).json({
          success: false,
          error: 'Fingerprint quality is too low. Please try again.'
        });
      }

      // Create biometric data
      const biometricData = await BiometricData.create({
        staff_id,
        fingerprint_template,
        template_quality,
        finger_position,
        device_id,
        enrollment_date: new Date()
      });

      // Update staff biometric enrollment status
      await staff.update({ is_biometric_enrolled: true });

      // Log enrollment
      await AuditLog.logBiometricAction(
        staff_id, 
        'enroll_fingerprint', 
        biometricData.toJSON(), 
        req.ip
      );

      logger.logBiometric('enrollment', staff_id, device_id, true);

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('biometric_enrolled', {
        staff_id,
        enrollment_date: biometricData.enrollment_date
      });

      res.status(201).json({
        success: true,
        message: 'Fingerprint enrolled successfully',
        data: {
          staff_id,
          template_quality,
          finger_position,
          enrollment_date: biometricData.enrollment_date
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Verify fingerprint
  static async verifyFingerprint(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { 
        staff_id, 
        fingerprint_template, 
        device_id,
        action = 'attendance' // attendance, access_control, etc.
      } = req.body;

      // Get stored biometric data
      const biometricData = await BiometricData.findOne({
        where: { staff_id, is_active: true }
      });

      if (!biometricData) {
        logger.logBiometric('verification', staff_id, device_id, false);
        return res.status(404).json({
          success: false,
          error: 'No biometric data found for this staff member'
        });
      }

      // Simulate fingerprint matching (in real implementation, use biometric SDK)
      const isMatch = await this.simulateFingerprintMatch(
        fingerprint_template, 
        biometricData.getDecryptedTemplate()
      );

      // Update verification statistics
      await biometricData.updateVerification(isMatch);

      if (isMatch) {
        // Log successful verification
        await AuditLog.logBiometricAction(
          staff_id, 
          'verify_fingerprint', 
          { action, device_id, success: true }, 
          req.ip
        );

        logger.logBiometric('verification', staff_id, device_id, true);

        res.json({
          success: true,
          message: 'Fingerprint verified successfully',
          data: {
            staff_id,
            verification_time: new Date(),
            action
          }
        });
      } else {
        // Log failed verification
        await AuditLog.logBiometricAction(
          staff_id, 
          'verify_fingerprint', 
          { action, device_id, success: false }, 
          req.ip
        );

        logger.logBiometric('verification', staff_id, device_id, false);

        res.status(401).json({
          success: false,
          error: 'Fingerprint verification failed'
        });
      }

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get biometric data for staff
  static async getBiometricData(req, res) {
    try {
      const { staff_id } = req.params;

      const biometricData = await BiometricData.findOne({
        where: { staff_id },
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id']
        }]
      });

      if (!biometricData) {
        return res.status(404).json({
          success: false,
          error: 'No biometric data found'
        });
      }

      // Return security info without sensitive template data
      res.json({
        success: true,
        data: biometricData.getSecurityInfo()
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Update biometric data
  static async updateBiometricData(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { staff_id } = req.params;
      const { 
        fingerprint_template, 
        template_quality, 
        finger_position,
        device_id 
      } = req.body;

      const biometricData = await BiometricData.findOne({
        where: { staff_id }
      });

      if (!biometricData) {
        return res.status(404).json({
          success: false,
          error: 'No biometric data found'
        });
      }

      // Validate template quality
      if (template_quality < 70) {
        return res.status(400).json({
          success: false,
          error: 'Fingerprint quality is too low. Please try again.'
        });
      }

      const oldValues = biometricData.toJSON();

      // Update biometric data
      await biometricData.update({
        fingerprint_template,
        template_quality,
        finger_position,
        device_id,
        enrollment_date: new Date(), // Reset enrollment date
        verification_count: 0, // Reset verification count
        failed_attempts: 0 // Reset failed attempts
      });

      // Log update
      await AuditLog.logBiometricAction(
        staff_id, 
        'update_biometric', 
        biometricData.toJSON(), 
        req.ip
      );

      logger.logBiometric('update', staff_id, device_id, true);

      res.json({
        success: true,
        message: 'Biometric data updated successfully',
        data: biometricData.getSecurityInfo()
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Delete biometric data
  static async deleteBiometricData(req, res) {
    try {
      const { staff_id } = req.params;

      const biometricData = await BiometricData.findOne({
        where: { staff_id }
      });

      if (!biometricData) {
        return res.status(404).json({
          success: false,
          error: 'No biometric data found'
        });
      }

      // Soft delete by deactivating
      await biometricData.update({ is_active: false });

      // Update staff enrollment status
      const staff = await Staff.findByPk(staff_id);
      if (staff) {
        await staff.update({ is_biometric_enrolled: false });
      }

      // Log deletion
      await AuditLog.logBiometricAction(
        staff_id, 
        'delete_biometric', 
        { staff_id }, 
        req.ip
      );

      logger.logBiometric('deletion', staff_id, null, true);

      res.json({
        success: true,
        message: 'Biometric data deleted successfully'
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get biometric statistics
  static async getBiometricStats(req, res) {
    try {
      const totalEnrolled = await BiometricData.count({
        where: { is_active: true }
      });

      const totalStaff = await Staff.count({
        where: { status: 'active' }
      });

      const enrollmentRate = totalStaff > 0 ? 
        ((totalEnrolled / totalStaff) * 100).toFixed(2) : 0;

      // Quality distribution
      const qualityStats = await BiometricData.findAll({
        attributes: [
          [BiometricData.sequelize.fn('AVG', BiometricData.sequelize.col('template_quality')), 'avg_quality'],
          [BiometricData.sequelize.fn('MIN', BiometricData.sequelize.col('template_quality')), 'min_quality'],
          [BiometricData.sequelize.fn('MAX', BiometricData.sequelize.col('template_quality')), 'max_quality']
        ],
        where: { is_active: true }
      });

      res.json({
        success: true,
        data: {
          total_enrolled: totalEnrolled,
          total_staff: totalStaff,
          enrollment_rate: parseFloat(enrollmentRate),
          quality_stats: qualityStats[0]?.dataValues || {}
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Simulate fingerprint matching (replace with actual biometric SDK)
  static async simulateFingerprintMatch(template1, template2) {
    // In a real implementation, this would use a biometric SDK
    // For simulation, we'll do a simple string comparison with some randomness
    if (!template1 || !template2) return false;
    
    // Simulate 95% accuracy for exact matches, 5% false positive rate
    if (template1 === template2) {
      return Math.random() > 0.05; // 95% success rate for correct templates
    } else {
      return Math.random() < 0.05; // 5% false positive rate
    }
  }
}

module.exports = BiometricController;
