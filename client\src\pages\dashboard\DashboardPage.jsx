import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  UsersIcon, 
  ClockIcon, 
  CalendarDaysIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { reportsAPI, attendanceAPI } from '../../services/api'
import { useAuthStore } from '../../stores/authStore'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const DashboardPage = () => {
  const { user, hasPermission } = useAuthStore()

  // Fetch dashboard data
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: () => reportsAPI.getDashboard(),
    enabled: hasPermission('view_reports'),
    refetchInterval: 30000 // Refresh every 30 seconds
  })

  // Fetch today's attendance
  const { data: todayAttendance, isLoading: attendanceLoading } = useQuery({
    queryKey: ['today-attendance'],
    queryFn: () => attendanceAPI.getToday(),
    enabled: hasPermission('view_attendance'),
    refetchInterval: 60000 // Refresh every minute
  })

  const stats = dashboardData?.data?.data
  const attendance = todayAttendance?.data?.data

  const quickStats = [
    {
      name: 'Total Staff',
      value: stats?.staff?.total_staff || 0,
      icon: UsersIcon,
      color: 'bg-blue-500',
      change: '+2.1%',
      changeType: 'positive'
    },
    {
      name: 'Present Today',
      value: stats?.today?.total_present || 0,
      icon: CheckCircleIcon,
      color: 'bg-green-500',
      change: `${stats?.today?.attendance_rate || 0}%`,
      changeType: 'neutral'
    },
    {
      name: 'Late Today',
      value: stats?.today?.total_late || 0,
      icon: ExclamationTriangleIcon,
      color: 'bg-yellow-500',
      change: '-5.4%',
      changeType: 'positive'
    },
    {
      name: 'Pending Leaves',
      value: stats?.leaves?.pending_approvals || 0,
      icon: CalendarDaysIcon,
      color: 'bg-purple-500',
      change: '+1.2%',
      changeType: 'negative'
    }
  ]

  if (dashboardLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.first_name}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's what's happening with your team today.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {quickStats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 ${stat.color} rounded-md flex items-center justify-center`}>
                    <stat.icon className="w-5 h-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'positive' ? 'text-green-600' :
                        stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {stat.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Attendance */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Today's Attendance</h3>
            <p className="card-description">
              Real-time attendance status
            </p>
          </div>
          <div className="card-content">
            {attendanceLoading ? (
              <LoadingSpinner size="md" />
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Present</span>
                  <span className="text-lg font-semibold text-green-600">
                    {attendance?.summary?.total_present || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Late</span>
                  <span className="text-lg font-semibold text-yellow-600">
                    {attendance?.summary?.total_late || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Still in Office</span>
                  <span className="text-lg font-semibold text-blue-600">
                    {attendance?.summary?.still_in_office || 0}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Activity</h3>
            <p className="card-description">
              Latest attendance and leave activities
            </p>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {attendance?.attendance?.slice(0, 5).map((record, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    record.status === 'present' ? 'bg-green-500' :
                    record.status === 'late' ? 'bg-yellow-500' : 'bg-gray-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {record.staff?.first_name} {record.staff?.last_name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {record.time_in ? `Checked in at ${record.time_in}` : 'Not checked in'}
                    </p>
                  </div>
                  <div className={`badge ${
                    record.status === 'present' ? 'badge-success' :
                    record.status === 'late' ? 'badge-warning' : 'badge-gray'
                  }`}>
                    {record.status}
                  </div>
                </div>
              )) || (
                <p className="text-sm text-gray-500 text-center py-4">
                  No attendance records today
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Quick Actions</h3>
          <p className="card-description">
            Common tasks and shortcuts
          </p>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {hasPermission('manage_staff') && (
              <button className="btn btn-outline btn-md">
                <UsersIcon className="w-5 h-5 mr-2" />
                Add Staff
              </button>
            )}
            {hasPermission('mark_attendance') && (
              <button className="btn btn-outline btn-md">
                <ClockIcon className="w-5 h-5 mr-2" />
                Mark Attendance
              </button>
            )}
            {hasPermission('apply_leave') && (
              <button className="btn btn-outline btn-md">
                <CalendarDaysIcon className="w-5 h-5 mr-2" />
                Apply Leave
              </button>
            )}
            {hasPermission('view_reports') && (
              <button className="btn btn-outline btn-md">
                <ChartBarIcon className="w-5 h-5 mr-2" />
                View Reports
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
