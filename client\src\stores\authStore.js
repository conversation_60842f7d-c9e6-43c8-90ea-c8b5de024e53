import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authAPI } from '../services/api'
import toast from 'react-hot-toast'

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      permissions: [],

      // Actions
      login: async (credentials) => {
        try {
          set({ isLoading: true })
          
          const response = await authAPI.login(credentials)
          const { staff, tokens } = response.data
          
          // Extract permissions from role
          const permissions = staff.role?.permissions || []
          
          set({
            user: staff,
            tokens,
            isAuthenticated: true,
            permissions,
            isLoading: false
          })
          
          toast.success('Login successful!')
          return { success: true }
          
        } catch (error) {
          set({ isLoading: false })
          const message = error.response?.data?.error || 'Login failed'
          toast.error(message)
          return { success: false, error: message }
        }
      },

      logout: async () => {
        try {
          // Call logout API to blacklist token
          await authAPI.logout()
        } catch (error) {
          console.error('Logout API error:', error)
        } finally {
          // Clear state regardless of API call result
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            permissions: [],
            isLoading: false
          })
          toast.success('Logged out successfully')
        }
      },

      refreshToken: async () => {
        try {
          const { tokens } = get()
          if (!tokens?.refreshToken) {
            throw new Error('No refresh token available')
          }

          const response = await authAPI.refreshToken(tokens.refreshToken)
          const newTokens = {
            ...tokens,
            accessToken: response.data.accessToken
          }

          set({ tokens: newTokens })
          return newTokens.accessToken

        } catch (error) {
          console.error('Token refresh failed:', error)
          // If refresh fails, logout user
          get().logout()
          throw error
        }
      },

      updateProfile: async (profileData) => {
        try {
          set({ isLoading: true })
          
          const response = await authAPI.updateProfile(profileData)
          const updatedUser = response.data
          
          set({
            user: updatedUser,
            isLoading: false
          })
          
          toast.success('Profile updated successfully!')
          return { success: true }
          
        } catch (error) {
          set({ isLoading: false })
          const message = error.response?.data?.error || 'Profile update failed'
          toast.error(message)
          return { success: false, error: message }
        }
      },

      changePassword: async (passwordData) => {
        try {
          set({ isLoading: true })
          
          await authAPI.changePassword(passwordData)
          
          set({ isLoading: false })
          toast.success('Password changed successfully! Please login again.')
          
          // Logout user after password change
          setTimeout(() => {
            get().logout()
          }, 2000)
          
          return { success: true }
          
        } catch (error) {
          set({ isLoading: false })
          const message = error.response?.data?.error || 'Password change failed'
          toast.error(message)
          return { success: false, error: message }
        }
      },

      verifyToken: async () => {
        try {
          const { tokens } = get()
          if (!tokens?.accessToken) {
            return false
          }

          await authAPI.verifyToken()
          return true

        } catch (error) {
          console.error('Token verification failed:', error)
          return false
        }
      },

      // Helper methods
      hasPermission: (permission) => {
        const { permissions, user } = get()
        
        // Admin has all permissions
        if (user?.role?.is_admin) {
          return true
        }
        
        // Check if user has specific permission
        return permissions.includes(permission) || permissions.includes('*')
      },

      hasAnyPermission: (permissionList) => {
        const { hasPermission } = get()
        return permissionList.some(permission => hasPermission(permission))
      },

      isAdmin: () => {
        const { user } = get()
        return user?.role?.is_admin || false
      },

      isManager: () => {
        const { user } = get()
        return user?.role?.is_manager || user?.role?.is_admin || false
      },

      // Initialize auth state from stored data
      initialize: async () => {
        try {
          set({ isLoading: true })
          
          const { tokens } = get()
          if (!tokens?.accessToken) {
            set({ isLoading: false })
            return
          }

          // Verify token and get current user data
          const isValid = await get().verifyToken()
          if (!isValid) {
            // Token is invalid, clear auth state
            get().logout()
            return
          }

          // Get fresh user profile
          const response = await authAPI.getProfile()
          const { staff, permissions } = response.data
          
          set({
            user: staff,
            permissions,
            isAuthenticated: true,
            isLoading: false
          })

        } catch (error) {
          console.error('Auth initialization failed:', error)
          get().logout()
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
        permissions: state.permissions
      })
    }
  )
)

// Initialize auth state on app load
if (typeof window !== 'undefined') {
  useAuthStore.getState().initialize()
}

export { useAuthStore }
