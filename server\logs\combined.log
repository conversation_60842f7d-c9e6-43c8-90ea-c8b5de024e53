{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:38:50"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:38:57"}
{"level":"info","message":"Redis connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:38:57"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"staff-attendance-api","timestamp":"2025-07-09 19:38:57"}
{"level":"info","message":"Biometric device DEVICE-001 initialized (simulation mode)","service":"staff-attendance-api","timestamp":"2025-07-09 19:39:38"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:39:50"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:39:57"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:40:10"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:40:18"}
{"level":"info","message":"GET / - ::1","service":"staff-attendance-api","timestamp":"2025-07-09 19:40:41"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"staff-attendance-api","timestamp":"2025-07-09 19:40:41"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:04"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:05"}
{"level":"info","message":"GET /api/docs - ::1","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:08"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:21"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:21"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:54"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:41:54"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:42:29"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:42:29"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:42:57"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:42:57"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:44:09"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:44:09"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:44:38"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:44:38"}
{"level":"info","message":"GET / - ::1","service":"staff-attendance-api","timestamp":"2025-07-09 19:47:14"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"staff-attendance-api","timestamp":"2025-07-09 19:47:14"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:49:26"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:49:26"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 19:49:56"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 19:49:56"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 20:06:09"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 20:06:09"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 20:15:11"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 20:15:11"}
{"level":"info","message":"Database connection established successfully","service":"staff-attendance-api","timestamp":"2025-07-09 20:26:14"}
{"level":"info","message":"Database models synchronized","service":"staff-attendance-api","timestamp":"2025-07-09 20:26:14"}
