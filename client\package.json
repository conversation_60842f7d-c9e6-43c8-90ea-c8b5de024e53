{"name": "staff-attendance-client", "version": "1.0.0", "description": "Frontend React application for Staff Attendance Management System", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@tanstack/react-query": "^5.8.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@headlessui/react": "^1.7.17", "framer-motion": "^10.16.16", "react-table": "^7.8.0", "react-datepicker": "^4.24.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "vitest": "^0.34.6", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5"}, "keywords": ["react", "attendance", "dashboard", "biometric", "frontend"], "author": "Staff Management Team", "license": "MIT"}