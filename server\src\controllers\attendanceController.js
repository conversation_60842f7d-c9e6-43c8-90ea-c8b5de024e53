const { Attendance, Staff, AuditLog } = require('../models');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

class AttendanceController {
  // Check in
  static async checkIn(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { staff_id, verification_method = 'fingerprint', device_id, location } = req.body;
      const today = new Date().toISOString().split('T')[0];

      // Check if staff exists and is active
      const staff = await Staff.findByPk(staff_id);
      if (!staff || staff.status !== 'active') {
        return res.status(404).json({
          success: false,
          error: 'Staff not found or inactive'
        });
      }

      // Check if already checked in today
      const existingAttendance = await Attendance.findOne({
        where: {
          staff_id,
          date: today
        }
      });

      if (existingAttendance && existingAttendance.time_in) {
        return res.status(400).json({
          success: false,
          error: 'Already checked in today',
          data: existingAttendance
        });
      }

      const currentTime = new Date().toTimeString().split(' ')[0];
      const shiftStart = staff.shift_start;
      
      // Calculate if late
      const timeIn = new Date(`1970-01-01T${currentTime}`);
      const shiftStartTime = new Date(`1970-01-01T${shiftStart}`);
      const isLate = timeIn > shiftStartTime;
      const lateMinutes = isLate ? Math.floor((timeIn - shiftStartTime) / (1000 * 60)) : 0;

      const attendanceData = {
        staff_id,
        date: today,
        time_in: currentTime,
        status: isLate ? 'late' : 'present',
        late_minutes: lateMinutes,
        verification_method,
        device_id,
        location
      };

      let attendance;
      if (existingAttendance) {
        // Update existing record
        attendance = await existingAttendance.update(attendanceData);
      } else {
        // Create new record
        attendance = await Attendance.create(attendanceData);
      }

      // Log attendance
      await AuditLog.logAttendance(staff_id, 'checkin', attendance.toJSON(), req.ip);
      logger.logAttendance('checkin', staff_id, { time: currentTime, late: isLate });

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('attendance_checkin', {
        attendance: attendance.toJSON(),
        staff: staff.toJSON()
      });

      res.json({
        success: true,
        message: `Check-in successful${isLate ? ' (Late)' : ''}`,
        data: attendance
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Check out
  static async checkOut(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { staff_id, verification_method = 'fingerprint', device_id } = req.body;
      const today = new Date().toISOString().split('T')[0];

      // Find today's attendance record
      const attendance = await Attendance.findOne({
        where: {
          staff_id,
          date: today
        }
      });

      if (!attendance || !attendance.time_in) {
        return res.status(400).json({
          success: false,
          error: 'No check-in record found for today'
        });
      }

      if (attendance.time_out) {
        return res.status(400).json({
          success: false,
          error: 'Already checked out today',
          data: attendance
        });
      }

      const currentTime = new Date().toTimeString().split(' ')[0];
      
      // Get staff shift info
      const staff = await Staff.findByPk(staff_id);
      const shiftEnd = staff.shift_end;
      
      // Calculate if early departure
      const timeOut = new Date(`1970-01-01T${currentTime}`);
      const shiftEndTime = new Date(`1970-01-01T${shiftEnd}`);
      const isEarlyDeparture = timeOut < shiftEndTime;
      const earlyDepartureMinutes = isEarlyDeparture ? 
        Math.floor((shiftEndTime - timeOut) / (1000 * 60)) : 0;

      // Calculate total hours
      const timeIn = new Date(`1970-01-01T${attendance.time_in}`);
      const diffMs = timeOut - timeIn;
      const totalHours = diffMs / (1000 * 60 * 60);
      const workingHours = Math.max(0, totalHours - (attendance.break_duration || 0));

      const updateData = {
        time_out: currentTime,
        total_hours: workingHours,
        early_departure_minutes: earlyDepartureMinutes,
        verification_method,
        device_id
      };

      await attendance.update(updateData);

      // Log attendance
      await AuditLog.logAttendance(staff_id, 'checkout', attendance.toJSON(), req.ip);
      logger.logAttendance('checkout', staff_id, { 
        time: currentTime, 
        total_hours: workingHours,
        early_departure: isEarlyDeparture 
      });

      // Emit real-time update
      const io = req.app.get('io');
      io.to('admin-room').emit('attendance_checkout', {
        attendance: attendance.toJSON(),
        staff: staff.toJSON()
      });

      res.json({
        success: true,
        message: `Check-out successful${isEarlyDeparture ? ' (Early departure)' : ''}`,
        data: attendance
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get attendance records with filtering
  static async getAttendance(req, res) {
    try {
      const {
        staff_id,
        start_date,
        end_date,
        status,
        page = 1,
        limit = 10,
        sortBy = 'date',
        sortOrder = 'DESC'
      } = req.query;

      const whereClause = {};
      const offset = (page - 1) * limit;

      // Add filters
      if (staff_id) {
        whereClause.staff_id = staff_id;
      }

      if (start_date && end_date) {
        whereClause.date = {
          [Op.between]: [start_date, end_date]
        };
      } else if (start_date) {
        whereClause.date = {
          [Op.gte]: start_date
        };
      } else if (end_date) {
        whereClause.date = {
          [Op.lte]: end_date
        };
      }

      if (status) {
        whereClause.status = status;
      }

      const { count, rows: attendance } = await Attendance.findAndCountAll({
        where: whereClause,
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
          include: ['department']
        }],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      res.json({
        success: true,
        data: {
          attendance,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get today's attendance
  static async getTodayAttendance(req, res) {
    try {
      const today = new Date().toISOString().split('T')[0];

      const attendance = await Attendance.findAll({
        where: { date: today },
        include: [{
          model: Staff,
          as: 'staff',
          attributes: ['staff_id', 'first_name', 'last_name', 'employee_id'],
          include: ['department']
        }],
        order: [['time_in', 'ASC']]
      });

      // Get summary statistics
      const totalPresent = attendance.filter(a => a.time_in).length;
      const totalLate = attendance.filter(a => a.status === 'late').length;
      const totalCheckedOut = attendance.filter(a => a.time_out).length;

      res.json({
        success: true,
        data: {
          attendance,
          summary: {
            total_present: totalPresent,
            total_late: totalLate,
            total_checked_out: totalCheckedOut,
            still_in_office: totalPresent - totalCheckedOut
          }
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get attendance statistics
  static async getAttendanceStats(req, res) {
    try {
      const { start_date, end_date, staff_id } = req.query;
      const whereClause = {};

      if (staff_id) {
        whereClause.staff_id = staff_id;
      }

      if (start_date && end_date) {
        whereClause.date = {
          [Op.between]: [start_date, end_date]
        };
      }

      const totalDays = await Attendance.count({ where: whereClause });
      const presentDays = await Attendance.count({ 
        where: { ...whereClause, status: 'present' } 
      });
      const lateDays = await Attendance.count({ 
        where: { ...whereClause, status: 'late' } 
      });
      const absentDays = await Attendance.count({ 
        where: { ...whereClause, status: 'absent' } 
      });

      // Average working hours
      const avgHours = await Attendance.findOne({
        where: { ...whereClause, total_hours: { [Op.not]: null } },
        attributes: [
          [Attendance.sequelize.fn('AVG', Attendance.sequelize.col('total_hours')), 'avg_hours']
        ]
      });

      res.json({
        success: true,
        data: {
          total_days: totalDays,
          present_days: presentDays,
          late_days: lateDays,
          absent_days: absentDays,
          attendance_rate: totalDays > 0 ? ((presentDays + lateDays) / totalDays * 100).toFixed(2) : 0,
          punctuality_rate: (presentDays + lateDays) > 0 ? (presentDays / (presentDays + lateDays) * 100).toFixed(2) : 0,
          average_working_hours: avgHours?.dataValues?.avg_hours ? 
            parseFloat(avgHours.dataValues.avg_hours).toFixed(2) : 0
        }
      });

    } catch (error) {
      logger.logError(error, req);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = AttendanceController;
